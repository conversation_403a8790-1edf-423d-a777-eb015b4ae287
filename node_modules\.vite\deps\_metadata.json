{"hash": "6fa61c67", "configHash": "53fe6b1f", "lockfileHash": "fac6f3a1", "browserHash": "165a9580", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "70f28d05", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "a7b8e4a6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b6a776c9", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "fb19b07f", "needsInterop": true}, "@google/generative-ai": {"src": "../../@google/generative-ai/dist/index.mjs", "file": "@google_generative-ai.js", "fileHash": "e6f8691b", "needsInterop": false}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "d8972c7f", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "90b1f668", "needsInterop": true}}, "chunks": {"chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}