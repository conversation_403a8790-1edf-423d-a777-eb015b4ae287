import { FC } from "react";
import MonacoEditor from "@monaco-editor/react";

interface EditorProps {
  originalPrompt: string;
  enhancedPrompt: string;
  isLoading: boolean;
  onPromptChange: (prompt: string) => void;
  onEnhance: () => void;
}

export const Editor: FC<EditorProps> = ({
  originalPrompt,
  enhancedPrompt,
  isLoading,
  onPromptChange,
  onEnhance,
}) => {
  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
    }
  };

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Input Section */}
      <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Original Prompt
          </h2>
          <button
            onClick={onEnhance}
            disabled={isLoading || !originalPrompt.trim()}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              isLoading || !originalPrompt.trim()
                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                : "bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
            }`}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Enhancing...</span>
              </div>
            ) : (
              "Enhance Prompt"
            )}
          </button>
        </div>
        <div className="h-64">
          <MonacoEditor
            height="100%"
            defaultLanguage="plaintext"
            value={originalPrompt}
            onChange={(value) => onPromptChange(value || "")}
            options={{
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              fontSize: 14,
              lineNumbers: "off",
              wordWrap: "on",
              padding: { top: 16, bottom: 16 },
              theme: "vs",
            }}
          />
        </div>
      </div>

      {/* Output Section */}
      <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Enhanced Prompt
          </h2>
          {enhancedPrompt && (
            <div className="flex space-x-2">
              <button
                onClick={() => handleCopyToClipboard(enhancedPrompt)}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded hover:bg-gray-50"
              >
                Copy
              </button>
              <button
                onClick={() => onPromptChange(enhancedPrompt)}
                className="px-3 py-1 text-sm text-primary-600 hover:text-primary-700 border border-primary-300 rounded hover:bg-primary-50"
              >
                Use as Input
              </button>
            </div>
          )}
        </div>
        <div className="h-64">
          {enhancedPrompt ? (
            <MonacoEditor
              height="100%"
              defaultLanguage="plaintext"
              value={enhancedPrompt}
              options={{
                readOnly: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 14,
                lineNumbers: "off",
                wordWrap: "on",
                padding: { top: 16, bottom: 16 },
                theme: "vs",
              }}
            />
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              <div className="text-center">
                <p className="text-lg mb-2">Enhanced prompt will appear here</p>
                <p className="text-sm">
                  Enter a prompt above and click "Enhance Prompt" to get started
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex justify-between items-center bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
        <div className="flex space-x-4 text-sm text-gray-600">
          <span>Characters: {originalPrompt.length}</span>
          <span>
            Words:{" "}
            {
              originalPrompt.split(/\s+/).filter((word) => word.length > 0)
                .length
            }
          </span>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => onPromptChange("")}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded hover:bg-gray-50"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  );
};
