"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const utils_1 = require("./utils");
const dotenv = __importStar(require("dotenv"));
dotenv.config();
class AppWindow {
    constructor() {
        this.mainWindow = null;
        electron_1.app.whenReady().then(() => {
            this.createMainWindow();
            electron_1.app.on("activate", () => {
                if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });
        electron_1.app.on("window-all-closed", () => {
            if (process.platform !== "darwin") {
                electron_1.app.quit();
            }
        });
        this.setupIpcHandlers();
    }
    createMainWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, "preload.js"),
            },
            titleBarStyle: "default",
            show: false,
        });
        // Load the React app
        if ((0, utils_1.isDev)()) {
            this.mainWindow.loadURL("http://localhost:5173");
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile(path.join(__dirname, "../react/index.html"));
        }
        this.mainWindow.once("ready-to-show", () => {
            this.mainWindow?.show();
        });
        this.mainWindow.on("closed", () => {
            this.mainWindow = null;
        });
    }
    setupIpcHandlers() {
        const userDataPath = electron_1.app.getPath("userData");
        const configPath = path.join(userDataPath, "app-config.json");
        const readConfig = () => {
            if (fs.existsSync(configPath)) {
                try {
                    return JSON.parse(fs.readFileSync(configPath, "utf-8"));
                }
                catch (e) {
                    console.error("Failed to parse config file:", e);
                    return {};
                }
            }
            return {};
        };
        const writeConfig = (config) => {
            try {
                fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
            }
            catch (e) {
                console.error("Failed to write config file:", e);
            }
        };
        // Handle API key storage/retrieval
        electron_1.ipcMain.handle("store-api-key", async (_, apiKey) => {
            const config = readConfig();
            config.apiKey = apiKey;
            writeConfig(config);
            return { success: true };
        });
        electron_1.ipcMain.handle("get-api-key", async () => {
            if ((0, utils_1.isDev)()) {
                return { apiKey: process.env.GEMINI_API_KEY || null };
            }
            else {
                const config = readConfig();
                return { apiKey: config.apiKey || null };
            }
        });
        // Handle model selection storage/retrieval
        electron_1.ipcMain.handle("store-selected-model", async (_, modelId) => {
            const config = readConfig();
            config.selectedModel = modelId;
            writeConfig(config);
            return { success: true };
        });
        electron_1.ipcMain.handle("get-selected-model", async () => {
            const config = readConfig();
            return { modelId: config.selectedModel || null };
        });
        // Handle file operations
        electron_1.ipcMain.handle("show-save-dialog", async () => {
            if (!this.mainWindow)
                return { canceled: true };
            const result = await electron_1.dialog.showSaveDialog(this.mainWindow, {
                filters: [
                    { name: "Text Files", extensions: ["txt"] },
                    { name: "All Files", extensions: ["*"] },
                ],
            });
            return result;
        });
        electron_1.ipcMain.handle("is-dev", () => {
            return (0, utils_1.isDev)();
        });
        electron_1.ipcMain.handle("show-open-dialog", async () => {
            if (!this.mainWindow)
                return { canceled: true };
            const result = await electron_1.dialog.showOpenDialog(this.mainWindow, {
                properties: ["openFile"],
                filters: [
                    { name: "Text Files", extensions: ["txt"] },
                    { name: "All Files", extensions: ["*"] },
                ],
            });
            return result;
        });
    }
}
// Initialize the application
new AppWindow();
