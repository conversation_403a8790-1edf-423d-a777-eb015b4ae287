import { FC, useState, useEffect } from "react";
import { GeminiModel } from "../types";
import { geminiService } from "../services/geminiService";

interface ModelSelectorProps {
  selectedModel: string | null;
  onModelSelect: (modelId: string) => void;
  disabled?: boolean;
}

export const ModelSelector: FC<ModelSelectorProps> = ({
  selectedModel,
  onModelSelect,
  disabled = false,
}) => {
  const [models, setModels] = useState<GeminiModel[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    if (!geminiService.isInitialized()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await geminiService.listModels();

      setModels(response.models);

      // If no model is selected, select the first stable model
      if (!selectedModel && response.models.length > 0) {
        // Prefer stable models (non-preview, non-experimental)
        const stableModels = response.models.filter((model) => {
          const modelId =
            model.baseModelId || model.name.replace("models/", "");
          return (
            !modelId.includes("preview") &&
            !modelId.includes("exp") &&
            !modelId.includes("experimental")
          );
        });

        const modelToSelect =
          stableModels.length > 0 ? stableModels[0] : response.models[0];
        const modelId =
          modelToSelect.baseModelId ||
          modelToSelect.name.replace("models/", "");

        onModelSelect(modelId);
      }
    } catch (err) {
      console.error("Failed to load models:", err);
      setError(err instanceof Error ? err.message : "Failed to load models");
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelChange = async (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const modelId = event.target.value;

    // Validate model before selecting
    const isValid = await geminiService.validateModel(modelId);
    if (!isValid) {
      setError(
        `Model ${modelId} is not available. Please select a different model.`
      );
      return;
    }

    setError(null);
    onModelSelect(modelId);
  };

  const getModelDisplayName = (model: GeminiModel) => {
    return model.displayName || model.baseModelId;
  };

  const getModelDescription = (model: GeminiModel) => {
    const tokenInfo = `${
      model.inputTokenLimit?.toLocaleString() || "N/A"
    } input tokens`;
    return model.description
      ? `${model.description} (${tokenInfo})`
      : tokenInfo;
  };

  if (error) {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Model Selection
        </label>
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex items-center">
            <div className="text-red-600 text-sm">
              <strong>Error loading models:</strong> {error}
            </div>
            <button
              onClick={loadModels}
              className="ml-auto text-red-600 hover:text-red-800 text-sm underline"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Model Selection
      </label>
      <div className="relative">
        <select
          value={selectedModel || ""}
          onChange={handleModelChange}
          disabled={disabled || isLoading || models.length === 0}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <option value="">Loading models...</option>
          ) : models.length === 0 ? (
            <option value="">No models available</option>
          ) : (
            <>
              <option value="">Select a model</option>
              {models.map((model) => {
                // Use baseModelId if available, otherwise extract from name
                const modelId =
                  model.baseModelId || model.name.replace("models/", "");
                return (
                  <option key={modelId} value={modelId}>
                    {getModelDisplayName(model)}
                  </option>
                );
              })}
            </>
          )}
        </select>

        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {selectedModel && models.length > 0 && (
        <div className="mt-2 text-sm text-gray-600">
          {(() => {
            const model = models.find((m) => {
              const modelId = m.baseModelId || m.name.replace("models/", "");
              return modelId === selectedModel;
            });
            return model ? (
              <div>
                <div className="font-medium">{getModelDisplayName(model)}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {getModelDescription(model)}
                </div>
              </div>
            ) : null;
          })()}
        </div>
      )}
    </div>
  );
};
