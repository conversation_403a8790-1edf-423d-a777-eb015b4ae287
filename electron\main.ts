import { app, BrowserWindow, ipc<PERSON>ain, dialog } from "electron";
import * as path from "path";
import * as fs from "fs";
import { isDev } from "./utils";
import * as dotenv from "dotenv";

dotenv.config();

class AppWindow {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    app.whenReady().then(() => {
      this.createMainWindow();

      app.on("activate", () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on("window-all-closed", () => {
      if (process.platform !== "darwin") {
        app.quit();
      }
    });

    this.setupIpcHandlers();
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, "preload.js"),
      },
      titleBarStyle: "default",
      show: false,
    });

    // Load the React app
    if (isDev()) {
      this.mainWindow.loadURL("http://localhost:5173");
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, "../react/index.html"));
    }

    this.mainWindow.once("ready-to-show", () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on("closed", () => {
      this.mainWindow = null;
    });
  }

  private setupIpcHandlers(): void {
    const userDataPath = app.getPath("userData");
    const configPath = path.join(userDataPath, "app-config.json");

    const readConfig = () => {
      if (fs.existsSync(configPath)) {
        try {
          return JSON.parse(fs.readFileSync(configPath, "utf-8"));
        } catch (e) {
          console.error("Failed to parse config file:", e);
          return {};
        }
      }
      return {};
    };

    const writeConfig = (config: any) => {
      try {
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      } catch (e) {
        console.error("Failed to write config file:", e);
      }
    };

    // Handle API key storage/retrieval
    ipcMain.handle("store-api-key", async (_, apiKey: string) => {
      const config = readConfig();
      config.apiKey = apiKey;
      writeConfig(config);
      return { success: true };
    });

    ipcMain.handle("get-api-key", async () => {
      if (isDev()) {
        return { apiKey: process.env.GEMINI_API_KEY || null };
      } else {
        const config = readConfig();
        return { apiKey: config.apiKey || null };
      }
    });

    // Handle model selection storage/retrieval
    ipcMain.handle("store-selected-model", async (_, modelId: string) => {
      const config = readConfig();
      config.selectedModel = modelId;
      writeConfig(config);
      return { success: true };
    });

    ipcMain.handle("get-selected-model", async () => {
      const config = readConfig();
      return { modelId: config.selectedModel || null };
    });

    // Handle file operations
    ipcMain.handle("show-save-dialog", async () => {
      if (!this.mainWindow) return { canceled: true };

      const result = await dialog.showSaveDialog(this.mainWindow, {
        filters: [
          { name: "Text Files", extensions: ["txt"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      return result;
    });

    ipcMain.handle("is-dev", () => {
      return isDev();
    });

    ipcMain.handle("show-open-dialog", async () => {
      if (!this.mainWindow) return { canceled: true };

      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ["openFile"],
        filters: [
          { name: "Text Files", extensions: ["txt"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      return result;
    });
  }
}

// Initialize the application
new AppWindow();
