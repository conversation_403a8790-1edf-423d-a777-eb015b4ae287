import { FC, useState } from "react";
import MonacoE<PERSON>or from "@monaco-editor/react";
import { PromptTemplate } from "../types";

interface TemplateEditorProps {
  originalPrompt: string;
  selectedTemplate: PromptTemplate | null;
  onPromptChange: (prompt: string) => void;
  onTemplateSelect: (template: PromptTemplate | null) => void;
  isLoading: boolean;
  onEnhance: () => void;
}

// Predefined templates for common use cases
const PREDEFINED_TEMPLATES: PromptTemplate[] = [
  {
    id: "content-creation",
    name: "Content Creation",
    description: "Template for creating various types of content",
    category: "Writing",
    sections: {
      role: "You are a professional content creator and copywriter",
      context: "[CONTENT_TYPE] for [TARGET_AUDIENCE] in the [INDUSTRY] industry",
      instructions: "Create [CONTENT_TYPE] that:\n- [REQUIREMENT_1]\n- [REQUIREMENT_2]\n- [REQUIREMENT_3]",
      goal: "Produce engaging [CONTENT_TYPE] that [DESIRED_OUTCOME]",
      constraints: "- Length: [WORD_COUNT] words\n- Tone: [TONE]\n- Style: [STYLE]",
      examples: "[EXAMPLE_INPUT] → [EXAMPLE_OUTPUT]",
      outputFormat: "Format the response as [FORMAT_TYPE]"
    },
    placeholders: {
      "[CONTENT_TYPE]": "blog post, social media post, email, etc.",
      "[TARGET_AUDIENCE]": "professionals, students, general public, etc.",
      "[INDUSTRY]": "technology, healthcare, finance, etc.",
      "[REQUIREMENT_1]": "first requirement",
      "[REQUIREMENT_2]": "second requirement", 
      "[REQUIREMENT_3]": "third requirement",
      "[DESIRED_OUTCOME]": "drives engagement, educates, converts, etc.",
      "[WORD_COUNT]": "500, 1000, 2000, etc.",
      "[TONE]": "professional, casual, friendly, etc.",
      "[STYLE]": "informative, persuasive, entertaining, etc.",
      "[EXAMPLE_INPUT]": "sample input",
      "[EXAMPLE_OUTPUT]": "sample output",
      "[FORMAT_TYPE]": "markdown, HTML, plain text, etc."
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "code-review",
    name: "Code Review",
    description: "Template for code analysis and review",
    category: "Development",
    sections: {
      role: "You are a senior software engineer and code reviewer",
      context: "Reviewing [LANGUAGE] code for [PROJECT_TYPE]",
      instructions: "Analyze the code for:\n- Code quality and best practices\n- Performance issues\n- Security vulnerabilities\n- Maintainability",
      goal: "Provide constructive feedback to improve code quality",
      constraints: "- Focus on [FOCUS_AREAS]\n- Provide specific examples\n- Suggest improvements",
      examples: "",
      outputFormat: "Structured review with sections for each analysis area"
    },
    placeholders: {
      "[LANGUAGE]": "JavaScript, Python, Java, etc.",
      "[PROJECT_TYPE]": "web application, API, mobile app, etc.",
      "[FOCUS_AREAS]": "security, performance, readability, etc."
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "data-analysis",
    name: "Data Analysis",
    description: "Template for data analysis and insights",
    category: "Analytics",
    sections: {
      role: "You are a data analyst and insights specialist",
      context: "Analyzing [DATA_TYPE] data from [DATA_SOURCE]",
      instructions: "Perform analysis to:\n- Identify key patterns and trends\n- Generate actionable insights\n- Highlight anomalies or outliers",
      goal: "Provide clear, actionable insights from the data",
      constraints: "- Use statistical methods appropriate for [DATA_TYPE]\n- Present findings clearly\n- Include confidence levels",
      examples: "",
      outputFormat: "Executive summary followed by detailed analysis"
    },
    placeholders: {
      "[DATA_TYPE]": "sales, user behavior, financial, etc.",
      "[DATA_SOURCE]": "database, CSV file, API, etc."
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export const TemplateEditor: FC<TemplateEditorProps> = ({
  originalPrompt,
  selectedTemplate,
  onPromptChange,
  onTemplateSelect,
  isLoading,
  onEnhance,
}) => {
  const [showTemplateList, setShowTemplateList] = useState(false);

  const generateTemplatePrompt = (template: PromptTemplate): string => {
    const sections = template.sections;
    const parts: string[] = [];
    
    if (sections.role) parts.push(`**ROLE:** ${sections.role}`);
    if (sections.context) parts.push(`**CONTEXT:** ${sections.context}`);
    if (sections.instructions) parts.push(`**INSTRUCTIONS:** ${sections.instructions}`);
    if (sections.goal) parts.push(`**GOAL:** ${sections.goal}`);
    if (sections.constraints) parts.push(`**CONSTRAINTS:** ${sections.constraints}`);
    if (sections.examples) parts.push(`**EXAMPLES:** ${sections.examples}`);
    if (sections.outputFormat) parts.push(`**OUTPUT FORMAT:** ${sections.outputFormat}`);
    
    return parts.join('\n\n');
  };

  const handleTemplateSelect = (template: PromptTemplate) => {
    onTemplateSelect(template);
    onPromptChange(generateTemplatePrompt(template));
    setShowTemplateList(false);
  };

  const handleClearTemplate = () => {
    onTemplateSelect(null);
    onPromptChange("");
  };

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-900">Template Mode</h2>
            {selectedTemplate && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded">
                {selectedTemplate.name}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowTemplateList(!showTemplateList)}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded hover:bg-gray-50"
            >
              {showTemplateList ? "Hide Templates" : "Browse Templates"}
            </button>
            {selectedTemplate && (
              <button
                onClick={handleClearTemplate}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded hover:bg-gray-50"
              >
                Clear Template
              </button>
            )}
            <button
              onClick={onEnhance}
              disabled={isLoading || !originalPrompt.trim()}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                isLoading || !originalPrompt.trim()
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Creating Template...</span>
                </div>
              ) : (
                "Create Template"
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Template List */}
      {showTemplateList && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Available Templates</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {PREDEFINED_TEMPLATES.map((template) => (
              <div
                key={template.id}
                onClick={() => handleTemplateSelect(template)}
                className="p-3 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 cursor-pointer transition-colors"
              >
                <h4 className="font-medium text-gray-900">{template.name}</h4>
                <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                <span className="inline-block mt-2 px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  {template.category}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Editor */}
      <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900">
            {selectedTemplate ? "Template Content (Customize as needed)" : "Template Prompt"}
          </h3>
        </div>
        <div className="h-96">
          <MonacoEditor
            height="100%"
            defaultLanguage="markdown"
            value={originalPrompt}
            onChange={(value) => onPromptChange(value || "")}
            options={{
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              fontSize: 14,
              lineNumbers: "off",
              wordWrap: "on",
              padding: { top: 16, bottom: 16 },
              theme: "vs",
            }}
          />
        </div>
      </div>

      {/* Placeholder Help */}
      {selectedTemplate && (
        <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
          <h4 className="text-sm font-semibold text-blue-900 mb-2">Template Placeholders</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            {Object.entries(selectedTemplate.placeholders).map(([placeholder, description]) => (
              <div key={placeholder} className="flex">
                <code className="text-blue-700 font-mono mr-2">{placeholder}:</code>
                <span className="text-blue-600">{description}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
