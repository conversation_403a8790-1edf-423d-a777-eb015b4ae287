var tf=Object.defineProperty;var nf=(e,t,n)=>t in e?tf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Er=(e,t,n)=>nf(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function rf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Rs={exports:{}},Nl={},Ms={exports:{}},A={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dr=Symbol.for("react.element"),lf=Symbol.for("react.portal"),of=Symbol.for("react.fragment"),uf=Symbol.for("react.strict_mode"),sf=Symbol.for("react.profiler"),af=Symbol.for("react.provider"),cf=Symbol.for("react.context"),ff=Symbol.for("react.forward_ref"),df=Symbol.for("react.suspense"),pf=Symbol.for("react.memo"),mf=Symbol.for("react.lazy"),su=Symbol.iterator;function hf(e){return e===null||typeof e!="object"?null:(e=su&&e[su]||e["@@iterator"],typeof e=="function"?e:null)}var Ls={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},zs=Object.assign,As={};function wn(e,t,n){this.props=e,this.context=t,this.refs=As,this.updater=n||Ls}wn.prototype.isReactComponent={};wn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};wn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ds(){}Ds.prototype=wn.prototype;function pi(e,t,n){this.props=e,this.context=t,this.refs=As,this.updater=n||Ls}var mi=pi.prototype=new Ds;mi.constructor=pi;zs(mi,wn.prototype);mi.isPureReactComponent=!0;var au=Array.isArray,Fs=Object.prototype.hasOwnProperty,hi={current:null},$s={key:!0,ref:!0,__self:!0,__source:!0};function Us(e,t,n){var r,l={},o=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Fs.call(t,r)&&!$s.hasOwnProperty(r)&&(l[r]=t[r]);var u=arguments.length-2;if(u===1)l.children=n;else if(1<u){for(var s=Array(u),c=0;c<u;c++)s[c]=arguments[c+2];l.children=s}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)l[r]===void 0&&(l[r]=u[r]);return{$$typeof:dr,type:e,key:o,ref:i,props:l,_owner:hi.current}}function vf(e,t){return{$$typeof:dr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function vi(e){return typeof e=="object"&&e!==null&&e.$$typeof===dr}function gf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var cu=/\/+/g;function Vl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?gf(""+e.key):t.toString(36)}function Hr(e,t,n,r,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case dr:case lf:i=!0}}if(i)return i=e,l=l(i),e=r===""?"."+Vl(i,0):r,au(l)?(n="",e!=null&&(n=e.replace(cu,"$&/")+"/"),Hr(l,t,n,"",function(c){return c})):l!=null&&(vi(l)&&(l=vf(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(cu,"$&/")+"/")+e)),t.push(l)),1;if(i=0,r=r===""?".":r+":",au(e))for(var u=0;u<e.length;u++){o=e[u];var s=r+Vl(o,u);i+=Hr(o,t,n,s,l)}else if(s=hf(e),typeof s=="function")for(e=s.call(e),u=0;!(o=e.next()).done;)o=o.value,s=r+Vl(o,u++),i+=Hr(o,t,n,s,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function xr(e,t,n){if(e==null)return e;var r=[],l=0;return Hr(e,r,"","",function(o){return t.call(n,o,l++)}),r}function yf(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var de={current:null},Br={transition:null},wf={ReactCurrentDispatcher:de,ReactCurrentBatchConfig:Br,ReactCurrentOwner:hi};function Hs(){throw Error("act(...) is not supported in production builds of React.")}A.Children={map:xr,forEach:function(e,t,n){xr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return xr(e,function(){t++}),t},toArray:function(e){return xr(e,function(t){return t})||[]},only:function(e){if(!vi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};A.Component=wn;A.Fragment=of;A.Profiler=sf;A.PureComponent=pi;A.StrictMode=uf;A.Suspense=df;A.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=wf;A.act=Hs;A.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=zs({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=hi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(s in t)Fs.call(t,s)&&!$s.hasOwnProperty(s)&&(r[s]=t[s]===void 0&&u!==void 0?u[s]:t[s])}var s=arguments.length-2;if(s===1)r.children=n;else if(1<s){u=Array(s);for(var c=0;c<s;c++)u[c]=arguments[c+2];r.children=u}return{$$typeof:dr,type:e.type,key:l,ref:o,props:r,_owner:i}};A.createContext=function(e){return e={$$typeof:cf,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:af,_context:e},e.Consumer=e};A.createElement=Us;A.createFactory=function(e){var t=Us.bind(null,e);return t.type=e,t};A.createRef=function(){return{current:null}};A.forwardRef=function(e){return{$$typeof:ff,render:e}};A.isValidElement=vi;A.lazy=function(e){return{$$typeof:mf,_payload:{_status:-1,_result:e},_init:yf}};A.memo=function(e,t){return{$$typeof:pf,type:e,compare:t===void 0?null:t}};A.startTransition=function(e){var t=Br.transition;Br.transition={};try{e()}finally{Br.transition=t}};A.unstable_act=Hs;A.useCallback=function(e,t){return de.current.useCallback(e,t)};A.useContext=function(e){return de.current.useContext(e)};A.useDebugValue=function(){};A.useDeferredValue=function(e){return de.current.useDeferredValue(e)};A.useEffect=function(e,t){return de.current.useEffect(e,t)};A.useId=function(){return de.current.useId()};A.useImperativeHandle=function(e,t,n){return de.current.useImperativeHandle(e,t,n)};A.useInsertionEffect=function(e,t){return de.current.useInsertionEffect(e,t)};A.useLayoutEffect=function(e,t){return de.current.useLayoutEffect(e,t)};A.useMemo=function(e,t){return de.current.useMemo(e,t)};A.useReducer=function(e,t,n){return de.current.useReducer(e,t,n)};A.useRef=function(e){return de.current.useRef(e)};A.useState=function(e){return de.current.useState(e)};A.useSyncExternalStore=function(e,t,n){return de.current.useSyncExternalStore(e,t,n)};A.useTransition=function(){return de.current.useTransition()};A.version="18.3.1";Ms.exports=A;var j=Ms.exports;const Lt=rf(j);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sf=j,Ef=Symbol.for("react.element"),xf=Symbol.for("react.fragment"),kf=Object.prototype.hasOwnProperty,Cf=Sf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,_f={key:!0,ref:!0,__self:!0,__source:!0};function Bs(e,t,n){var r,l={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)kf.call(t,r)&&!_f.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:Ef,type:e,key:o,ref:i,props:l,_owner:Cf.current}}Nl.Fragment=xf;Nl.jsx=Bs;Nl.jsxs=Bs;Rs.exports=Nl;var w=Rs.exports,So={},Vs={exports:{}},_e={},Ks={exports:{}},Ws={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(_,I){var L=_.length;_.push(I);e:for(;0<L;){var T=L-1>>>1,M=_[T];if(0<l(M,I))_[T]=I,_[L]=M,L=T;else break e}}function n(_){return _.length===0?null:_[0]}function r(_){if(_.length===0)return null;var I=_[0],L=_.pop();if(L!==I){_[0]=L;e:for(var T=0,M=_.length,ee=M>>>1;T<ee;){var we=2*(T+1)-1,Ve=_[we],Pt=we+1,Sr=_[Pt];if(0>l(Ve,L))Pt<M&&0>l(Sr,Ve)?(_[T]=Sr,_[Pt]=L,T=Pt):(_[T]=Ve,_[we]=L,T=we);else if(Pt<M&&0>l(Sr,L))_[T]=Sr,_[Pt]=L,T=Pt;else break e}}return I}function l(_,I){var L=_.sortIndex-I.sortIndex;return L!==0?L:_.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,u=i.now();e.unstable_now=function(){return i.now()-u}}var s=[],c=[],v=1,h=null,p=3,m=!1,g=!1,E=!1,$=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function d(_){for(var I=n(c);I!==null;){if(I.callback===null)r(c);else if(I.startTime<=_)r(c),I.sortIndex=I.expirationTime,t(s,I);else break;I=n(c)}}function y(_){if(E=!1,d(_),!g)if(n(s)!==null)g=!0,U(x);else{var I=n(c);I!==null&&Pe(y,I.startTime-_)}}function x(_,I){g=!1,E&&(E=!1,f(O),O=-1),m=!0;var L=p;try{for(d(I),h=n(s);h!==null&&(!(h.expirationTime>I)||_&&!b());){var T=h.callback;if(typeof T=="function"){h.callback=null,p=h.priorityLevel;var M=T(h.expirationTime<=I);I=e.unstable_now(),typeof M=="function"?h.callback=M:h===n(s)&&r(s),d(I)}else r(s);h=n(s)}if(h!==null)var ee=!0;else{var we=n(c);we!==null&&Pe(y,we.startTime-I),ee=!1}return ee}finally{h=null,p=L,m=!1}}var C=!1,k=null,O=-1,z=5,P=-1;function b(){return!(e.unstable_now()-P<z)}function He(){if(k!==null){var _=e.unstable_now();P=_;var I=!0;try{I=k(!0,_)}finally{I?Be():(C=!1,k=null)}}else C=!1}var Be;if(typeof a=="function")Be=function(){a(He)};else if(typeof MessageChannel<"u"){var ot=new MessageChannel,R=ot.port2;ot.port1.onmessage=He,Be=function(){R.postMessage(null)}}else Be=function(){$(He,0)};function U(_){k=_,C||(C=!0,Be())}function Pe(_,I){O=$(function(){_(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(_){_.callback=null},e.unstable_continueExecution=function(){g||m||(g=!0,U(x))},e.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<_?Math.floor(1e3/_):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(_){switch(p){case 1:case 2:case 3:var I=3;break;default:I=p}var L=p;p=I;try{return _()}finally{p=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(_,I){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var L=p;p=_;try{return I()}finally{p=L}},e.unstable_scheduleCallback=function(_,I,L){var T=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?T+L:T):L=T,_){case 1:var M=-1;break;case 2:M=250;break;case 5:M=**********;break;case 4:M=1e4;break;default:M=5e3}return M=L+M,_={id:v++,callback:I,priorityLevel:_,startTime:L,expirationTime:M,sortIndex:-1},L>T?(_.sortIndex=L,t(c,_),n(s)===null&&_===n(c)&&(E?(f(O),O=-1):E=!0,Pe(y,L-T))):(_.sortIndex=M,t(s,_),g||m||(g=!0,U(x))),_},e.unstable_shouldYield=b,e.unstable_wrapCallback=function(_){var I=p;return function(){var L=p;p=I;try{return _.apply(this,arguments)}finally{p=L}}}})(Ws);Ks.exports=Ws;var Nf=Ks.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pf=j,Ce=Nf;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Gs=new Set,Gn={};function Vt(e,t){dn(e,t),dn(e+"Capture",t)}function dn(e,t){for(Gn[e]=t,e=0;e<t.length;e++)Gs.add(t[e])}var et=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Eo=Object.prototype.hasOwnProperty,Of=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,fu={},du={};function Tf(e){return Eo.call(du,e)?!0:Eo.call(fu,e)?!1:Of.test(e)?du[e]=!0:(fu[e]=!0,!1)}function jf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function If(e,t,n,r){if(t===null||typeof t>"u"||jf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function pe(e,t,n,r,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var oe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){oe[e]=new pe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];oe[t]=new pe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){oe[e]=new pe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){oe[e]=new pe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){oe[e]=new pe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){oe[e]=new pe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){oe[e]=new pe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){oe[e]=new pe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){oe[e]=new pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var gi=/[\-:]([a-z])/g;function yi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(gi,yi);oe[t]=new pe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(gi,yi);oe[t]=new pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(gi,yi);oe[t]=new pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){oe[e]=new pe(e,1,!1,e.toLowerCase(),null,!1,!1)});oe.xlinkHref=new pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){oe[e]=new pe(e,1,!1,e.toLowerCase(),null,!0,!0)});function wi(e,t,n,r){var l=oe.hasOwnProperty(t)?oe[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(If(t,n,l,r)&&(n=null),r||l===null?Tf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var lt=Pf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,kr=Symbol.for("react.element"),Gt=Symbol.for("react.portal"),Qt=Symbol.for("react.fragment"),Si=Symbol.for("react.strict_mode"),xo=Symbol.for("react.profiler"),Qs=Symbol.for("react.provider"),Ys=Symbol.for("react.context"),Ei=Symbol.for("react.forward_ref"),ko=Symbol.for("react.suspense"),Co=Symbol.for("react.suspense_list"),xi=Symbol.for("react.memo"),ut=Symbol.for("react.lazy"),Xs=Symbol.for("react.offscreen"),pu=Symbol.iterator;function xn(e){return e===null||typeof e!="object"?null:(e=pu&&e[pu]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,Kl;function jn(e){if(Kl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Kl=t&&t[1]||""}return`
`+Kl+e}var Wl=!1;function Gl(e,t){if(!e||Wl)return"";Wl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),o=r.stack.split(`
`),i=l.length-1,u=o.length-1;1<=i&&0<=u&&l[i]!==o[u];)u--;for(;1<=i&&0<=u;i--,u--)if(l[i]!==o[u]){if(i!==1||u!==1)do if(i--,u--,0>u||l[i]!==o[u]){var s=`
`+l[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=i&&0<=u);break}}}finally{Wl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?jn(e):""}function Rf(e){switch(e.tag){case 5:return jn(e.type);case 16:return jn("Lazy");case 13:return jn("Suspense");case 19:return jn("SuspenseList");case 0:case 2:case 15:return e=Gl(e.type,!1),e;case 11:return e=Gl(e.type.render,!1),e;case 1:return e=Gl(e.type,!0),e;default:return""}}function _o(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Qt:return"Fragment";case Gt:return"Portal";case xo:return"Profiler";case Si:return"StrictMode";case ko:return"Suspense";case Co:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ys:return(e.displayName||"Context")+".Consumer";case Qs:return(e._context.displayName||"Context")+".Provider";case Ei:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case xi:return t=e.displayName||null,t!==null?t:_o(e.type)||"Memo";case ut:t=e._payload,e=e._init;try{return _o(e(t))}catch{}}return null}function Mf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return _o(t);case 8:return t===Si?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function xt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Js(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Lf(e){var t=Js(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Cr(e){e._valueTracker||(e._valueTracker=Lf(e))}function Zs(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Js(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function br(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function No(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function mu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=xt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function qs(e,t){t=t.checked,t!=null&&wi(e,"checked",t,!1)}function Po(e,t){qs(e,t);var n=xt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Oo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Oo(e,t.type,xt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function hu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Oo(e,t,n){(t!=="number"||br(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var In=Array.isArray;function on(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+xt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function To(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function vu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(S(92));if(In(n)){if(1<n.length)throw Error(S(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:xt(n)}}function bs(e,t){var n=xt(t.value),r=xt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function gu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ea(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function jo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ea(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var _r,ta=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(_r=_r||document.createElement("div"),_r.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=_r.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Qn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var zn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},zf=["Webkit","ms","Moz","O"];Object.keys(zn).forEach(function(e){zf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),zn[t]=zn[e]})});function na(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||zn.hasOwnProperty(e)&&zn[e]?(""+t).trim():t+"px"}function ra(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=na(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Af=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Io(e,t){if(t){if(Af[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function Ro(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Mo=null;function ki(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Lo=null,un=null,sn=null;function yu(e){if(e=hr(e)){if(typeof Lo!="function")throw Error(S(280));var t=e.stateNode;t&&(t=Il(t),Lo(e.stateNode,e.type,t))}}function la(e){un?sn?sn.push(e):sn=[e]:un=e}function oa(){if(un){var e=un,t=sn;if(sn=un=null,yu(e),t)for(e=0;e<t.length;e++)yu(t[e])}}function ia(e,t){return e(t)}function ua(){}var Ql=!1;function sa(e,t,n){if(Ql)return e(t,n);Ql=!0;try{return ia(e,t,n)}finally{Ql=!1,(un!==null||sn!==null)&&(ua(),oa())}}function Yn(e,t){var n=e.stateNode;if(n===null)return null;var r=Il(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(S(231,t,typeof n));return n}var zo=!1;if(et)try{var kn={};Object.defineProperty(kn,"passive",{get:function(){zo=!0}}),window.addEventListener("test",kn,kn),window.removeEventListener("test",kn,kn)}catch{zo=!1}function Df(e,t,n,r,l,o,i,u,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(v){this.onError(v)}}var An=!1,el=null,tl=!1,Ao=null,Ff={onError:function(e){An=!0,el=e}};function $f(e,t,n,r,l,o,i,u,s){An=!1,el=null,Df.apply(Ff,arguments)}function Uf(e,t,n,r,l,o,i,u,s){if($f.apply(this,arguments),An){if(An){var c=el;An=!1,el=null}else throw Error(S(198));tl||(tl=!0,Ao=c)}}function Kt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function aa(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function wu(e){if(Kt(e)!==e)throw Error(S(188))}function Hf(e){var t=e.alternate;if(!t){if(t=Kt(e),t===null)throw Error(S(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return wu(l),e;if(o===r)return wu(l),t;o=o.sibling}throw Error(S(188))}if(n.return!==r.return)n=l,r=o;else{for(var i=!1,u=l.child;u;){if(u===n){i=!0,n=l,r=o;break}if(u===r){i=!0,r=l,n=o;break}u=u.sibling}if(!i){for(u=o.child;u;){if(u===n){i=!0,n=o,r=l;break}if(u===r){i=!0,r=o,n=l;break}u=u.sibling}if(!i)throw Error(S(189))}}if(n.alternate!==r)throw Error(S(190))}if(n.tag!==3)throw Error(S(188));return n.stateNode.current===n?e:t}function ca(e){return e=Hf(e),e!==null?fa(e):null}function fa(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=fa(e);if(t!==null)return t;e=e.sibling}return null}var da=Ce.unstable_scheduleCallback,Su=Ce.unstable_cancelCallback,Bf=Ce.unstable_shouldYield,Vf=Ce.unstable_requestPaint,X=Ce.unstable_now,Kf=Ce.unstable_getCurrentPriorityLevel,Ci=Ce.unstable_ImmediatePriority,pa=Ce.unstable_UserBlockingPriority,nl=Ce.unstable_NormalPriority,Wf=Ce.unstable_LowPriority,ma=Ce.unstable_IdlePriority,Pl=null,Qe=null;function Gf(e){if(Qe&&typeof Qe.onCommitFiberRoot=="function")try{Qe.onCommitFiberRoot(Pl,e,void 0,(e.current.flags&128)===128)}catch{}}var Fe=Math.clz32?Math.clz32:Xf,Qf=Math.log,Yf=Math.LN2;function Xf(e){return e>>>=0,e===0?32:31-(Qf(e)/Yf|0)|0}var Nr=64,Pr=4194304;function Rn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function rl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var u=i&~l;u!==0?r=Rn(u):(o&=i,o!==0&&(r=Rn(o)))}else i=n&~l,i!==0?r=Rn(i):o!==0&&(r=Rn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Fe(t),l=1<<n,r|=e[n],t&=~l;return r}function Jf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-Fe(o),u=1<<i,s=l[i];s===-1?(!(u&n)||u&r)&&(l[i]=Jf(u,t)):s<=t&&(e.expiredLanes|=u),o&=~u}}function Do(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ha(){var e=Nr;return Nr<<=1,!(Nr&4194240)&&(Nr=64),e}function Yl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function pr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Fe(t),e[t]=n}function qf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Fe(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function _i(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Fe(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var F=0;function va(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var ga,Ni,ya,wa,Sa,Fo=!1,Or=[],pt=null,mt=null,ht=null,Xn=new Map,Jn=new Map,at=[],bf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Eu(e,t){switch(e){case"focusin":case"focusout":pt=null;break;case"dragenter":case"dragleave":mt=null;break;case"mouseover":case"mouseout":ht=null;break;case"pointerover":case"pointerout":Xn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Jn.delete(t.pointerId)}}function Cn(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=hr(t),t!==null&&Ni(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function ed(e,t,n,r,l){switch(t){case"focusin":return pt=Cn(pt,e,t,n,r,l),!0;case"dragenter":return mt=Cn(mt,e,t,n,r,l),!0;case"mouseover":return ht=Cn(ht,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return Xn.set(o,Cn(Xn.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,Jn.set(o,Cn(Jn.get(o)||null,e,t,n,r,l)),!0}return!1}function Ea(e){var t=It(e.target);if(t!==null){var n=Kt(t);if(n!==null){if(t=n.tag,t===13){if(t=aa(n),t!==null){e.blockedOn=t,Sa(e.priority,function(){ya(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Vr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=$o(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Mo=r,n.target.dispatchEvent(r),Mo=null}else return t=hr(n),t!==null&&Ni(t),e.blockedOn=n,!1;t.shift()}return!0}function xu(e,t,n){Vr(e)&&n.delete(t)}function td(){Fo=!1,pt!==null&&Vr(pt)&&(pt=null),mt!==null&&Vr(mt)&&(mt=null),ht!==null&&Vr(ht)&&(ht=null),Xn.forEach(xu),Jn.forEach(xu)}function _n(e,t){e.blockedOn===t&&(e.blockedOn=null,Fo||(Fo=!0,Ce.unstable_scheduleCallback(Ce.unstable_NormalPriority,td)))}function Zn(e){function t(l){return _n(l,e)}if(0<Or.length){_n(Or[0],e);for(var n=1;n<Or.length;n++){var r=Or[n];r.blockedOn===e&&(r.blockedOn=null)}}for(pt!==null&&_n(pt,e),mt!==null&&_n(mt,e),ht!==null&&_n(ht,e),Xn.forEach(t),Jn.forEach(t),n=0;n<at.length;n++)r=at[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<at.length&&(n=at[0],n.blockedOn===null);)Ea(n),n.blockedOn===null&&at.shift()}var an=lt.ReactCurrentBatchConfig,ll=!0;function nd(e,t,n,r){var l=F,o=an.transition;an.transition=null;try{F=1,Pi(e,t,n,r)}finally{F=l,an.transition=o}}function rd(e,t,n,r){var l=F,o=an.transition;an.transition=null;try{F=4,Pi(e,t,n,r)}finally{F=l,an.transition=o}}function Pi(e,t,n,r){if(ll){var l=$o(e,t,n,r);if(l===null)lo(e,t,r,ol,n),Eu(e,r);else if(ed(l,e,t,n,r))r.stopPropagation();else if(Eu(e,r),t&4&&-1<bf.indexOf(e)){for(;l!==null;){var o=hr(l);if(o!==null&&ga(o),o=$o(e,t,n,r),o===null&&lo(e,t,r,ol,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else lo(e,t,r,null,n)}}var ol=null;function $o(e,t,n,r){if(ol=null,e=ki(r),e=It(e),e!==null)if(t=Kt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=aa(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ol=e,null}function xa(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Kf()){case Ci:return 1;case pa:return 4;case nl:case Wf:return 16;case ma:return 536870912;default:return 16}default:return 16}}var ft=null,Oi=null,Kr=null;function ka(){if(Kr)return Kr;var e,t=Oi,n=t.length,r,l="value"in ft?ft.value:ft.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===l[o-r];r++);return Kr=l.slice(e,1<r?1-r:void 0)}function Wr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Tr(){return!0}function ku(){return!1}function Ne(e){function t(n,r,l,o,i){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(n=e[u],this[u]=n?n(o):o[u]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Tr:ku,this.isPropagationStopped=ku,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Tr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Tr)},persist:function(){},isPersistent:Tr}),t}var Sn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ti=Ne(Sn),mr=Q({},Sn,{view:0,detail:0}),ld=Ne(mr),Xl,Jl,Nn,Ol=Q({},mr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ji,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Nn&&(Nn&&e.type==="mousemove"?(Xl=e.screenX-Nn.screenX,Jl=e.screenY-Nn.screenY):Jl=Xl=0,Nn=e),Xl)},movementY:function(e){return"movementY"in e?e.movementY:Jl}}),Cu=Ne(Ol),od=Q({},Ol,{dataTransfer:0}),id=Ne(od),ud=Q({},mr,{relatedTarget:0}),Zl=Ne(ud),sd=Q({},Sn,{animationName:0,elapsedTime:0,pseudoElement:0}),ad=Ne(sd),cd=Q({},Sn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),fd=Ne(cd),dd=Q({},Sn,{data:0}),_u=Ne(dd),pd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},md={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hd={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function vd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=hd[e])?!!t[e]:!1}function ji(){return vd}var gd=Q({},mr,{key:function(e){if(e.key){var t=pd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Wr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?md[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ji,charCode:function(e){return e.type==="keypress"?Wr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Wr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),yd=Ne(gd),wd=Q({},Ol,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Nu=Ne(wd),Sd=Q({},mr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ji}),Ed=Ne(Sd),xd=Q({},Sn,{propertyName:0,elapsedTime:0,pseudoElement:0}),kd=Ne(xd),Cd=Q({},Ol,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),_d=Ne(Cd),Nd=[9,13,27,32],Ii=et&&"CompositionEvent"in window,Dn=null;et&&"documentMode"in document&&(Dn=document.documentMode);var Pd=et&&"TextEvent"in window&&!Dn,Ca=et&&(!Ii||Dn&&8<Dn&&11>=Dn),Pu=" ",Ou=!1;function _a(e,t){switch(e){case"keyup":return Nd.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Na(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Yt=!1;function Od(e,t){switch(e){case"compositionend":return Na(t);case"keypress":return t.which!==32?null:(Ou=!0,Pu);case"textInput":return e=t.data,e===Pu&&Ou?null:e;default:return null}}function Td(e,t){if(Yt)return e==="compositionend"||!Ii&&_a(e,t)?(e=ka(),Kr=Oi=ft=null,Yt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ca&&t.locale!=="ko"?null:t.data;default:return null}}var jd={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Tu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jd[e.type]:t==="textarea"}function Pa(e,t,n,r){la(r),t=il(t,"onChange"),0<t.length&&(n=new Ti("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Fn=null,qn=null;function Id(e){Fa(e,0)}function Tl(e){var t=Zt(e);if(Zs(t))return e}function Rd(e,t){if(e==="change")return t}var Oa=!1;if(et){var ql;if(et){var bl="oninput"in document;if(!bl){var ju=document.createElement("div");ju.setAttribute("oninput","return;"),bl=typeof ju.oninput=="function"}ql=bl}else ql=!1;Oa=ql&&(!document.documentMode||9<document.documentMode)}function Iu(){Fn&&(Fn.detachEvent("onpropertychange",Ta),qn=Fn=null)}function Ta(e){if(e.propertyName==="value"&&Tl(qn)){var t=[];Pa(t,qn,e,ki(e)),sa(Id,t)}}function Md(e,t,n){e==="focusin"?(Iu(),Fn=t,qn=n,Fn.attachEvent("onpropertychange",Ta)):e==="focusout"&&Iu()}function Ld(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Tl(qn)}function zd(e,t){if(e==="click")return Tl(t)}function Ad(e,t){if(e==="input"||e==="change")return Tl(t)}function Dd(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ue=typeof Object.is=="function"?Object.is:Dd;function bn(e,t){if(Ue(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!Eo.call(t,l)||!Ue(e[l],t[l]))return!1}return!0}function Ru(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Mu(e,t){var n=Ru(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ru(n)}}function ja(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ja(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ia(){for(var e=window,t=br();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=br(e.document)}return t}function Ri(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Fd(e){var t=Ia(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ja(n.ownerDocument.documentElement,n)){if(r!==null&&Ri(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=Mu(n,o);var i=Mu(n,r);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var $d=et&&"documentMode"in document&&11>=document.documentMode,Xt=null,Uo=null,$n=null,Ho=!1;function Lu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ho||Xt==null||Xt!==br(r)||(r=Xt,"selectionStart"in r&&Ri(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),$n&&bn($n,r)||($n=r,r=il(Uo,"onSelect"),0<r.length&&(t=new Ti("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Xt)))}function jr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Jt={animationend:jr("Animation","AnimationEnd"),animationiteration:jr("Animation","AnimationIteration"),animationstart:jr("Animation","AnimationStart"),transitionend:jr("Transition","TransitionEnd")},eo={},Ra={};et&&(Ra=document.createElement("div").style,"AnimationEvent"in window||(delete Jt.animationend.animation,delete Jt.animationiteration.animation,delete Jt.animationstart.animation),"TransitionEvent"in window||delete Jt.transitionend.transition);function jl(e){if(eo[e])return eo[e];if(!Jt[e])return e;var t=Jt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ra)return eo[e]=t[n];return e}var Ma=jl("animationend"),La=jl("animationiteration"),za=jl("animationstart"),Aa=jl("transitionend"),Da=new Map,zu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ct(e,t){Da.set(e,t),Vt(t,[e])}for(var to=0;to<zu.length;to++){var no=zu[to],Ud=no.toLowerCase(),Hd=no[0].toUpperCase()+no.slice(1);Ct(Ud,"on"+Hd)}Ct(Ma,"onAnimationEnd");Ct(La,"onAnimationIteration");Ct(za,"onAnimationStart");Ct("dblclick","onDoubleClick");Ct("focusin","onFocus");Ct("focusout","onBlur");Ct(Aa,"onTransitionEnd");dn("onMouseEnter",["mouseout","mouseover"]);dn("onMouseLeave",["mouseout","mouseover"]);dn("onPointerEnter",["pointerout","pointerover"]);dn("onPointerLeave",["pointerout","pointerover"]);Vt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Vt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Vt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Vt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Vt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Vt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bd=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mn));function Au(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Uf(r,t,void 0,e),e.currentTarget=null}function Fa(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var u=r[i],s=u.instance,c=u.currentTarget;if(u=u.listener,s!==o&&l.isPropagationStopped())break e;Au(l,u,c),o=s}else for(i=0;i<r.length;i++){if(u=r[i],s=u.instance,c=u.currentTarget,u=u.listener,s!==o&&l.isPropagationStopped())break e;Au(l,u,c),o=s}}}if(tl)throw e=Ao,tl=!1,Ao=null,e}function B(e,t){var n=t[Go];n===void 0&&(n=t[Go]=new Set);var r=e+"__bubble";n.has(r)||($a(t,e,2,!1),n.add(r))}function ro(e,t,n){var r=0;t&&(r|=4),$a(n,e,r,t)}var Ir="_reactListening"+Math.random().toString(36).slice(2);function er(e){if(!e[Ir]){e[Ir]=!0,Gs.forEach(function(n){n!=="selectionchange"&&(Bd.has(n)||ro(n,!1,e),ro(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ir]||(t[Ir]=!0,ro("selectionchange",!1,t))}}function $a(e,t,n,r){switch(xa(t)){case 1:var l=nd;break;case 4:l=rd;break;default:l=Pi}n=l.bind(null,t,n,e),l=void 0,!zo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function lo(e,t,n,r,l){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var u=r.stateNode.containerInfo;if(u===l||u.nodeType===8&&u.parentNode===l)break;if(i===4)for(i=r.return;i!==null;){var s=i.tag;if((s===3||s===4)&&(s=i.stateNode.containerInfo,s===l||s.nodeType===8&&s.parentNode===l))return;i=i.return}for(;u!==null;){if(i=It(u),i===null)return;if(s=i.tag,s===5||s===6){r=o=i;continue e}u=u.parentNode}}r=r.return}sa(function(){var c=o,v=ki(n),h=[];e:{var p=Da.get(e);if(p!==void 0){var m=Ti,g=e;switch(e){case"keypress":if(Wr(n)===0)break e;case"keydown":case"keyup":m=yd;break;case"focusin":g="focus",m=Zl;break;case"focusout":g="blur",m=Zl;break;case"beforeblur":case"afterblur":m=Zl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Cu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=id;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Ed;break;case Ma:case La:case za:m=ad;break;case Aa:m=kd;break;case"scroll":m=ld;break;case"wheel":m=_d;break;case"copy":case"cut":case"paste":m=fd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Nu}var E=(t&4)!==0,$=!E&&e==="scroll",f=E?p!==null?p+"Capture":null:p;E=[];for(var a=c,d;a!==null;){d=a;var y=d.stateNode;if(d.tag===5&&y!==null&&(d=y,f!==null&&(y=Yn(a,f),y!=null&&E.push(tr(a,y,d)))),$)break;a=a.return}0<E.length&&(p=new m(p,g,null,n,v),h.push({event:p,listeners:E}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",p&&n!==Mo&&(g=n.relatedTarget||n.fromElement)&&(It(g)||g[tt]))break e;if((m||p)&&(p=v.window===v?v:(p=v.ownerDocument)?p.defaultView||p.parentWindow:window,m?(g=n.relatedTarget||n.toElement,m=c,g=g?It(g):null,g!==null&&($=Kt(g),g!==$||g.tag!==5&&g.tag!==6)&&(g=null)):(m=null,g=c),m!==g)){if(E=Cu,y="onMouseLeave",f="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(E=Nu,y="onPointerLeave",f="onPointerEnter",a="pointer"),$=m==null?p:Zt(m),d=g==null?p:Zt(g),p=new E(y,a+"leave",m,n,v),p.target=$,p.relatedTarget=d,y=null,It(v)===c&&(E=new E(f,a+"enter",g,n,v),E.target=d,E.relatedTarget=$,y=E),$=y,m&&g)t:{for(E=m,f=g,a=0,d=E;d;d=Wt(d))a++;for(d=0,y=f;y;y=Wt(y))d++;for(;0<a-d;)E=Wt(E),a--;for(;0<d-a;)f=Wt(f),d--;for(;a--;){if(E===f||f!==null&&E===f.alternate)break t;E=Wt(E),f=Wt(f)}E=null}else E=null;m!==null&&Du(h,p,m,E,!1),g!==null&&$!==null&&Du(h,$,g,E,!0)}}e:{if(p=c?Zt(c):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var x=Rd;else if(Tu(p))if(Oa)x=Ad;else{x=Ld;var C=Md}else(m=p.nodeName)&&m.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(x=zd);if(x&&(x=x(e,c))){Pa(h,x,n,v);break e}C&&C(e,p,c),e==="focusout"&&(C=p._wrapperState)&&C.controlled&&p.type==="number"&&Oo(p,"number",p.value)}switch(C=c?Zt(c):window,e){case"focusin":(Tu(C)||C.contentEditable==="true")&&(Xt=C,Uo=c,$n=null);break;case"focusout":$n=Uo=Xt=null;break;case"mousedown":Ho=!0;break;case"contextmenu":case"mouseup":case"dragend":Ho=!1,Lu(h,n,v);break;case"selectionchange":if($d)break;case"keydown":case"keyup":Lu(h,n,v)}var k;if(Ii)e:{switch(e){case"compositionstart":var O="onCompositionStart";break e;case"compositionend":O="onCompositionEnd";break e;case"compositionupdate":O="onCompositionUpdate";break e}O=void 0}else Yt?_a(e,n)&&(O="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(O="onCompositionStart");O&&(Ca&&n.locale!=="ko"&&(Yt||O!=="onCompositionStart"?O==="onCompositionEnd"&&Yt&&(k=ka()):(ft=v,Oi="value"in ft?ft.value:ft.textContent,Yt=!0)),C=il(c,O),0<C.length&&(O=new _u(O,e,null,n,v),h.push({event:O,listeners:C}),k?O.data=k:(k=Na(n),k!==null&&(O.data=k)))),(k=Pd?Od(e,n):Td(e,n))&&(c=il(c,"onBeforeInput"),0<c.length&&(v=new _u("onBeforeInput","beforeinput",null,n,v),h.push({event:v,listeners:c}),v.data=k))}Fa(h,t)})}function tr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function il(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Yn(e,n),o!=null&&r.unshift(tr(e,o,l)),o=Yn(e,t),o!=null&&r.push(tr(e,o,l))),e=e.return}return r}function Wt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Du(e,t,n,r,l){for(var o=t._reactName,i=[];n!==null&&n!==r;){var u=n,s=u.alternate,c=u.stateNode;if(s!==null&&s===r)break;u.tag===5&&c!==null&&(u=c,l?(s=Yn(n,o),s!=null&&i.unshift(tr(n,s,u))):l||(s=Yn(n,o),s!=null&&i.push(tr(n,s,u)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Vd=/\r\n?/g,Kd=/\u0000|\uFFFD/g;function Fu(e){return(typeof e=="string"?e:""+e).replace(Vd,`
`).replace(Kd,"")}function Rr(e,t,n){if(t=Fu(t),Fu(e)!==t&&n)throw Error(S(425))}function ul(){}var Bo=null,Vo=null;function Ko(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wo=typeof setTimeout=="function"?setTimeout:void 0,Wd=typeof clearTimeout=="function"?clearTimeout:void 0,$u=typeof Promise=="function"?Promise:void 0,Gd=typeof queueMicrotask=="function"?queueMicrotask:typeof $u<"u"?function(e){return $u.resolve(null).then(e).catch(Qd)}:Wo;function Qd(e){setTimeout(function(){throw e})}function oo(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Zn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Zn(t)}function vt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Uu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var En=Math.random().toString(36).slice(2),Ge="__reactFiber$"+En,nr="__reactProps$"+En,tt="__reactContainer$"+En,Go="__reactEvents$"+En,Yd="__reactListeners$"+En,Xd="__reactHandles$"+En;function It(e){var t=e[Ge];if(t)return t;for(var n=e.parentNode;n;){if(t=n[tt]||n[Ge]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Uu(e);e!==null;){if(n=e[Ge])return n;e=Uu(e)}return t}e=n,n=e.parentNode}return null}function hr(e){return e=e[Ge]||e[tt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Zt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function Il(e){return e[nr]||null}var Qo=[],qt=-1;function _t(e){return{current:e}}function V(e){0>qt||(e.current=Qo[qt],Qo[qt]=null,qt--)}function H(e,t){qt++,Qo[qt]=e.current,e.current=t}var kt={},ae=_t(kt),ve=_t(!1),Dt=kt;function pn(e,t){var n=e.type.contextTypes;if(!n)return kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function ge(e){return e=e.childContextTypes,e!=null}function sl(){V(ve),V(ae)}function Hu(e,t,n){if(ae.current!==kt)throw Error(S(168));H(ae,t),H(ve,n)}function Ua(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(S(108,Mf(e)||"Unknown",l));return Q({},n,r)}function al(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||kt,Dt=ae.current,H(ae,e),H(ve,ve.current),!0}function Bu(e,t,n){var r=e.stateNode;if(!r)throw Error(S(169));n?(e=Ua(e,t,Dt),r.__reactInternalMemoizedMergedChildContext=e,V(ve),V(ae),H(ae,e)):V(ve),H(ve,n)}var Je=null,Rl=!1,io=!1;function Ha(e){Je===null?Je=[e]:Je.push(e)}function Jd(e){Rl=!0,Ha(e)}function Nt(){if(!io&&Je!==null){io=!0;var e=0,t=F;try{var n=Je;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Je=null,Rl=!1}catch(l){throw Je!==null&&(Je=Je.slice(e+1)),da(Ci,Nt),l}finally{F=t,io=!1}}return null}var bt=[],en=0,cl=null,fl=0,Oe=[],Te=0,Ft=null,Ze=1,qe="";function Ot(e,t){bt[en++]=fl,bt[en++]=cl,cl=e,fl=t}function Ba(e,t,n){Oe[Te++]=Ze,Oe[Te++]=qe,Oe[Te++]=Ft,Ft=e;var r=Ze;e=qe;var l=32-Fe(r)-1;r&=~(1<<l),n+=1;var o=32-Fe(t)+l;if(30<o){var i=l-l%5;o=(r&(1<<i)-1).toString(32),r>>=i,l-=i,Ze=1<<32-Fe(t)+l|n<<l|r,qe=o+e}else Ze=1<<o|n<<l|r,qe=e}function Mi(e){e.return!==null&&(Ot(e,1),Ba(e,1,0))}function Li(e){for(;e===cl;)cl=bt[--en],bt[en]=null,fl=bt[--en],bt[en]=null;for(;e===Ft;)Ft=Oe[--Te],Oe[Te]=null,qe=Oe[--Te],Oe[Te]=null,Ze=Oe[--Te],Oe[Te]=null}var ke=null,xe=null,K=!1,De=null;function Va(e,t){var n=je(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Vu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ke=e,xe=vt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ke=e,xe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Ft!==null?{id:Ze,overflow:qe}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=je(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ke=e,xe=null,!0):!1;default:return!1}}function Yo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Xo(e){if(K){var t=xe;if(t){var n=t;if(!Vu(e,t)){if(Yo(e))throw Error(S(418));t=vt(n.nextSibling);var r=ke;t&&Vu(e,t)?Va(r,n):(e.flags=e.flags&-4097|2,K=!1,ke=e)}}else{if(Yo(e))throw Error(S(418));e.flags=e.flags&-4097|2,K=!1,ke=e}}}function Ku(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ke=e}function Mr(e){if(e!==ke)return!1;if(!K)return Ku(e),K=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ko(e.type,e.memoizedProps)),t&&(t=xe)){if(Yo(e))throw Ka(),Error(S(418));for(;t;)Va(e,t),t=vt(t.nextSibling)}if(Ku(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){xe=vt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}xe=null}}else xe=ke?vt(e.stateNode.nextSibling):null;return!0}function Ka(){for(var e=xe;e;)e=vt(e.nextSibling)}function mn(){xe=ke=null,K=!1}function zi(e){De===null?De=[e]:De.push(e)}var Zd=lt.ReactCurrentBatchConfig;function Pn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(S(309));var r=n.stateNode}if(!r)throw Error(S(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var u=l.refs;i===null?delete u[o]:u[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(S(284));if(!n._owner)throw Error(S(290,e))}return e}function Lr(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Wu(e){var t=e._init;return t(e._payload)}function Wa(e){function t(f,a){if(e){var d=f.deletions;d===null?(f.deletions=[a],f.flags|=16):d.push(a)}}function n(f,a){if(!e)return null;for(;a!==null;)t(f,a),a=a.sibling;return null}function r(f,a){for(f=new Map;a!==null;)a.key!==null?f.set(a.key,a):f.set(a.index,a),a=a.sibling;return f}function l(f,a){return f=St(f,a),f.index=0,f.sibling=null,f}function o(f,a,d){return f.index=d,e?(d=f.alternate,d!==null?(d=d.index,d<a?(f.flags|=2,a):d):(f.flags|=2,a)):(f.flags|=1048576,a)}function i(f){return e&&f.alternate===null&&(f.flags|=2),f}function u(f,a,d,y){return a===null||a.tag!==6?(a=mo(d,f.mode,y),a.return=f,a):(a=l(a,d),a.return=f,a)}function s(f,a,d,y){var x=d.type;return x===Qt?v(f,a,d.props.children,y,d.key):a!==null&&(a.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===ut&&Wu(x)===a.type)?(y=l(a,d.props),y.ref=Pn(f,a,d),y.return=f,y):(y=qr(d.type,d.key,d.props,null,f.mode,y),y.ref=Pn(f,a,d),y.return=f,y)}function c(f,a,d,y){return a===null||a.tag!==4||a.stateNode.containerInfo!==d.containerInfo||a.stateNode.implementation!==d.implementation?(a=ho(d,f.mode,y),a.return=f,a):(a=l(a,d.children||[]),a.return=f,a)}function v(f,a,d,y,x){return a===null||a.tag!==7?(a=At(d,f.mode,y,x),a.return=f,a):(a=l(a,d),a.return=f,a)}function h(f,a,d){if(typeof a=="string"&&a!==""||typeof a=="number")return a=mo(""+a,f.mode,d),a.return=f,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case kr:return d=qr(a.type,a.key,a.props,null,f.mode,d),d.ref=Pn(f,null,a),d.return=f,d;case Gt:return a=ho(a,f.mode,d),a.return=f,a;case ut:var y=a._init;return h(f,y(a._payload),d)}if(In(a)||xn(a))return a=At(a,f.mode,d,null),a.return=f,a;Lr(f,a)}return null}function p(f,a,d,y){var x=a!==null?a.key:null;if(typeof d=="string"&&d!==""||typeof d=="number")return x!==null?null:u(f,a,""+d,y);if(typeof d=="object"&&d!==null){switch(d.$$typeof){case kr:return d.key===x?s(f,a,d,y):null;case Gt:return d.key===x?c(f,a,d,y):null;case ut:return x=d._init,p(f,a,x(d._payload),y)}if(In(d)||xn(d))return x!==null?null:v(f,a,d,y,null);Lr(f,d)}return null}function m(f,a,d,y,x){if(typeof y=="string"&&y!==""||typeof y=="number")return f=f.get(d)||null,u(a,f,""+y,x);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case kr:return f=f.get(y.key===null?d:y.key)||null,s(a,f,y,x);case Gt:return f=f.get(y.key===null?d:y.key)||null,c(a,f,y,x);case ut:var C=y._init;return m(f,a,d,C(y._payload),x)}if(In(y)||xn(y))return f=f.get(d)||null,v(a,f,y,x,null);Lr(a,y)}return null}function g(f,a,d,y){for(var x=null,C=null,k=a,O=a=0,z=null;k!==null&&O<d.length;O++){k.index>O?(z=k,k=null):z=k.sibling;var P=p(f,k,d[O],y);if(P===null){k===null&&(k=z);break}e&&k&&P.alternate===null&&t(f,k),a=o(P,a,O),C===null?x=P:C.sibling=P,C=P,k=z}if(O===d.length)return n(f,k),K&&Ot(f,O),x;if(k===null){for(;O<d.length;O++)k=h(f,d[O],y),k!==null&&(a=o(k,a,O),C===null?x=k:C.sibling=k,C=k);return K&&Ot(f,O),x}for(k=r(f,k);O<d.length;O++)z=m(k,f,O,d[O],y),z!==null&&(e&&z.alternate!==null&&k.delete(z.key===null?O:z.key),a=o(z,a,O),C===null?x=z:C.sibling=z,C=z);return e&&k.forEach(function(b){return t(f,b)}),K&&Ot(f,O),x}function E(f,a,d,y){var x=xn(d);if(typeof x!="function")throw Error(S(150));if(d=x.call(d),d==null)throw Error(S(151));for(var C=x=null,k=a,O=a=0,z=null,P=d.next();k!==null&&!P.done;O++,P=d.next()){k.index>O?(z=k,k=null):z=k.sibling;var b=p(f,k,P.value,y);if(b===null){k===null&&(k=z);break}e&&k&&b.alternate===null&&t(f,k),a=o(b,a,O),C===null?x=b:C.sibling=b,C=b,k=z}if(P.done)return n(f,k),K&&Ot(f,O),x;if(k===null){for(;!P.done;O++,P=d.next())P=h(f,P.value,y),P!==null&&(a=o(P,a,O),C===null?x=P:C.sibling=P,C=P);return K&&Ot(f,O),x}for(k=r(f,k);!P.done;O++,P=d.next())P=m(k,f,O,P.value,y),P!==null&&(e&&P.alternate!==null&&k.delete(P.key===null?O:P.key),a=o(P,a,O),C===null?x=P:C.sibling=P,C=P);return e&&k.forEach(function(He){return t(f,He)}),K&&Ot(f,O),x}function $(f,a,d,y){if(typeof d=="object"&&d!==null&&d.type===Qt&&d.key===null&&(d=d.props.children),typeof d=="object"&&d!==null){switch(d.$$typeof){case kr:e:{for(var x=d.key,C=a;C!==null;){if(C.key===x){if(x=d.type,x===Qt){if(C.tag===7){n(f,C.sibling),a=l(C,d.props.children),a.return=f,f=a;break e}}else if(C.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===ut&&Wu(x)===C.type){n(f,C.sibling),a=l(C,d.props),a.ref=Pn(f,C,d),a.return=f,f=a;break e}n(f,C);break}else t(f,C);C=C.sibling}d.type===Qt?(a=At(d.props.children,f.mode,y,d.key),a.return=f,f=a):(y=qr(d.type,d.key,d.props,null,f.mode,y),y.ref=Pn(f,a,d),y.return=f,f=y)}return i(f);case Gt:e:{for(C=d.key;a!==null;){if(a.key===C)if(a.tag===4&&a.stateNode.containerInfo===d.containerInfo&&a.stateNode.implementation===d.implementation){n(f,a.sibling),a=l(a,d.children||[]),a.return=f,f=a;break e}else{n(f,a);break}else t(f,a);a=a.sibling}a=ho(d,f.mode,y),a.return=f,f=a}return i(f);case ut:return C=d._init,$(f,a,C(d._payload),y)}if(In(d))return g(f,a,d,y);if(xn(d))return E(f,a,d,y);Lr(f,d)}return typeof d=="string"&&d!==""||typeof d=="number"?(d=""+d,a!==null&&a.tag===6?(n(f,a.sibling),a=l(a,d),a.return=f,f=a):(n(f,a),a=mo(d,f.mode,y),a.return=f,f=a),i(f)):n(f,a)}return $}var hn=Wa(!0),Ga=Wa(!1),dl=_t(null),pl=null,tn=null,Ai=null;function Di(){Ai=tn=pl=null}function Fi(e){var t=dl.current;V(dl),e._currentValue=t}function Jo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function cn(e,t){pl=e,Ai=tn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(he=!0),e.firstContext=null)}function Re(e){var t=e._currentValue;if(Ai!==e)if(e={context:e,memoizedValue:t,next:null},tn===null){if(pl===null)throw Error(S(308));tn=e,pl.dependencies={lanes:0,firstContext:e}}else tn=tn.next=e;return t}var Rt=null;function $i(e){Rt===null?Rt=[e]:Rt.push(e)}function Qa(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,$i(t)):(n.next=l.next,l.next=n),t.interleaved=n,nt(e,r)}function nt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var st=!1;function Ui(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ya(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function be(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function gt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,D&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,nt(e,n)}return l=r.interleaved,l===null?(t.next=t,$i(r)):(t.next=l.next,l.next=t),r.interleaved=t,nt(e,n)}function Gr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,_i(e,n)}}function Gu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ml(e,t,n,r){var l=e.updateQueue;st=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,u=l.shared.pending;if(u!==null){l.shared.pending=null;var s=u,c=s.next;s.next=null,i===null?o=c:i.next=c,i=s;var v=e.alternate;v!==null&&(v=v.updateQueue,u=v.lastBaseUpdate,u!==i&&(u===null?v.firstBaseUpdate=c:u.next=c,v.lastBaseUpdate=s))}if(o!==null){var h=l.baseState;i=0,v=c=s=null,u=o;do{var p=u.lane,m=u.eventTime;if((r&p)===p){v!==null&&(v=v.next={eventTime:m,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var g=e,E=u;switch(p=t,m=n,E.tag){case 1:if(g=E.payload,typeof g=="function"){h=g.call(m,h,p);break e}h=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=E.payload,p=typeof g=="function"?g.call(m,h,p):g,p==null)break e;h=Q({},h,p);break e;case 2:st=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,p=l.effects,p===null?l.effects=[u]:p.push(u))}else m={eventTime:m,lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},v===null?(c=v=m,s=h):v=v.next=m,i|=p;if(u=u.next,u===null){if(u=l.shared.pending,u===null)break;p=u,u=p.next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}while(!0);if(v===null&&(s=h),l.baseState=s,l.firstBaseUpdate=c,l.lastBaseUpdate=v,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);Ut|=i,e.lanes=i,e.memoizedState=h}}function Qu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(S(191,l));l.call(r)}}}var vr={},Ye=_t(vr),rr=_t(vr),lr=_t(vr);function Mt(e){if(e===vr)throw Error(S(174));return e}function Hi(e,t){switch(H(lr,t),H(rr,e),H(Ye,vr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:jo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=jo(t,e)}V(Ye),H(Ye,t)}function vn(){V(Ye),V(rr),V(lr)}function Xa(e){Mt(lr.current);var t=Mt(Ye.current),n=jo(t,e.type);t!==n&&(H(rr,e),H(Ye,n))}function Bi(e){rr.current===e&&(V(Ye),V(rr))}var W=_t(0);function hl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var uo=[];function Vi(){for(var e=0;e<uo.length;e++)uo[e]._workInProgressVersionPrimary=null;uo.length=0}var Qr=lt.ReactCurrentDispatcher,so=lt.ReactCurrentBatchConfig,$t=0,G=null,Z=null,te=null,vl=!1,Un=!1,or=0,qd=0;function ie(){throw Error(S(321))}function Ki(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ue(e[n],t[n]))return!1;return!0}function Wi(e,t,n,r,l,o){if($t=o,G=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Qr.current=e===null||e.memoizedState===null?np:rp,e=n(r,l),Un){o=0;do{if(Un=!1,or=0,25<=o)throw Error(S(301));o+=1,te=Z=null,t.updateQueue=null,Qr.current=lp,e=n(r,l)}while(Un)}if(Qr.current=gl,t=Z!==null&&Z.next!==null,$t=0,te=Z=G=null,vl=!1,t)throw Error(S(300));return e}function Gi(){var e=or!==0;return or=0,e}function We(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return te===null?G.memoizedState=te=e:te=te.next=e,te}function Me(){if(Z===null){var e=G.alternate;e=e!==null?e.memoizedState:null}else e=Z.next;var t=te===null?G.memoizedState:te.next;if(t!==null)te=t,Z=e;else{if(e===null)throw Error(S(310));Z=e,e={memoizedState:Z.memoizedState,baseState:Z.baseState,baseQueue:Z.baseQueue,queue:Z.queue,next:null},te===null?G.memoizedState=te=e:te=te.next=e}return te}function ir(e,t){return typeof t=="function"?t(e):t}function ao(e){var t=Me(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=Z,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var u=i=null,s=null,c=o;do{var v=c.lane;if(($t&v)===v)s!==null&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:v,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};s===null?(u=s=h,i=r):s=s.next=h,G.lanes|=v,Ut|=v}c=c.next}while(c!==null&&c!==o);s===null?i=r:s.next=u,Ue(r,t.memoizedState)||(he=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=s,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,G.lanes|=o,Ut|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function co(e){var t=Me(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);Ue(o,t.memoizedState)||(he=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ja(){}function Za(e,t){var n=G,r=Me(),l=t(),o=!Ue(r.memoizedState,l);if(o&&(r.memoizedState=l,he=!0),r=r.queue,Qi(ec.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||te!==null&&te.memoizedState.tag&1){if(n.flags|=2048,ur(9,ba.bind(null,n,r,l,t),void 0,null),ne===null)throw Error(S(349));$t&30||qa(n,t,l)}return l}function qa(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ba(e,t,n,r){t.value=n,t.getSnapshot=r,tc(t)&&nc(e)}function ec(e,t,n){return n(function(){tc(t)&&nc(e)})}function tc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ue(e,n)}catch{return!0}}function nc(e){var t=nt(e,1);t!==null&&$e(t,e,1,-1)}function Yu(e){var t=We();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ir,lastRenderedState:e},t.queue=e,e=e.dispatch=tp.bind(null,G,e),[t.memoizedState,e]}function ur(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function rc(){return Me().memoizedState}function Yr(e,t,n,r){var l=We();G.flags|=e,l.memoizedState=ur(1|t,n,void 0,r===void 0?null:r)}function Ml(e,t,n,r){var l=Me();r=r===void 0?null:r;var o=void 0;if(Z!==null){var i=Z.memoizedState;if(o=i.destroy,r!==null&&Ki(r,i.deps)){l.memoizedState=ur(t,n,o,r);return}}G.flags|=e,l.memoizedState=ur(1|t,n,o,r)}function Xu(e,t){return Yr(8390656,8,e,t)}function Qi(e,t){return Ml(2048,8,e,t)}function lc(e,t){return Ml(4,2,e,t)}function oc(e,t){return Ml(4,4,e,t)}function ic(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function uc(e,t,n){return n=n!=null?n.concat([e]):null,Ml(4,4,ic.bind(null,t,e),n)}function Yi(){}function sc(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ki(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ac(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ki(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function cc(e,t,n){return $t&21?(Ue(n,t)||(n=ha(),G.lanes|=n,Ut|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,he=!0),e.memoizedState=n)}function bd(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=so.transition;so.transition={};try{e(!1),t()}finally{F=n,so.transition=r}}function fc(){return Me().memoizedState}function ep(e,t,n){var r=wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},dc(e))pc(t,n);else if(n=Qa(e,t,n,r),n!==null){var l=fe();$e(n,e,r,l),mc(n,t,r)}}function tp(e,t,n){var r=wt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(dc(e))pc(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,u=o(i,n);if(l.hasEagerState=!0,l.eagerState=u,Ue(u,i)){var s=t.interleaved;s===null?(l.next=l,$i(t)):(l.next=s.next,s.next=l),t.interleaved=l;return}}catch{}finally{}n=Qa(e,t,l,r),n!==null&&(l=fe(),$e(n,e,r,l),mc(n,t,r))}}function dc(e){var t=e.alternate;return e===G||t!==null&&t===G}function pc(e,t){Un=vl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function mc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,_i(e,n)}}var gl={readContext:Re,useCallback:ie,useContext:ie,useEffect:ie,useImperativeHandle:ie,useInsertionEffect:ie,useLayoutEffect:ie,useMemo:ie,useReducer:ie,useRef:ie,useState:ie,useDebugValue:ie,useDeferredValue:ie,useTransition:ie,useMutableSource:ie,useSyncExternalStore:ie,useId:ie,unstable_isNewReconciler:!1},np={readContext:Re,useCallback:function(e,t){return We().memoizedState=[e,t===void 0?null:t],e},useContext:Re,useEffect:Xu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Yr(4194308,4,ic.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Yr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Yr(4,2,e,t)},useMemo:function(e,t){var n=We();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=We();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ep.bind(null,G,e),[r.memoizedState,e]},useRef:function(e){var t=We();return e={current:e},t.memoizedState=e},useState:Yu,useDebugValue:Yi,useDeferredValue:function(e){return We().memoizedState=e},useTransition:function(){var e=Yu(!1),t=e[0];return e=bd.bind(null,e[1]),We().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=G,l=We();if(K){if(n===void 0)throw Error(S(407));n=n()}else{if(n=t(),ne===null)throw Error(S(349));$t&30||qa(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Xu(ec.bind(null,r,o,e),[e]),r.flags|=2048,ur(9,ba.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=We(),t=ne.identifierPrefix;if(K){var n=qe,r=Ze;n=(r&~(1<<32-Fe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=or++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=qd++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},rp={readContext:Re,useCallback:sc,useContext:Re,useEffect:Qi,useImperativeHandle:uc,useInsertionEffect:lc,useLayoutEffect:oc,useMemo:ac,useReducer:ao,useRef:rc,useState:function(){return ao(ir)},useDebugValue:Yi,useDeferredValue:function(e){var t=Me();return cc(t,Z.memoizedState,e)},useTransition:function(){var e=ao(ir)[0],t=Me().memoizedState;return[e,t]},useMutableSource:Ja,useSyncExternalStore:Za,useId:fc,unstable_isNewReconciler:!1},lp={readContext:Re,useCallback:sc,useContext:Re,useEffect:Qi,useImperativeHandle:uc,useInsertionEffect:lc,useLayoutEffect:oc,useMemo:ac,useReducer:co,useRef:rc,useState:function(){return co(ir)},useDebugValue:Yi,useDeferredValue:function(e){var t=Me();return Z===null?t.memoizedState=e:cc(t,Z.memoizedState,e)},useTransition:function(){var e=co(ir)[0],t=Me().memoizedState;return[e,t]},useMutableSource:Ja,useSyncExternalStore:Za,useId:fc,unstable_isNewReconciler:!1};function ze(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Zo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ll={isMounted:function(e){return(e=e._reactInternals)?Kt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=fe(),l=wt(e),o=be(r,l);o.payload=t,n!=null&&(o.callback=n),t=gt(e,o,l),t!==null&&($e(t,e,l,r),Gr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=fe(),l=wt(e),o=be(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=gt(e,o,l),t!==null&&($e(t,e,l,r),Gr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=fe(),r=wt(e),l=be(n,r);l.tag=2,t!=null&&(l.callback=t),t=gt(e,l,r),t!==null&&($e(t,e,r,n),Gr(t,e,r))}};function Ju(e,t,n,r,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!bn(n,r)||!bn(l,o):!0}function hc(e,t,n){var r=!1,l=kt,o=t.contextType;return typeof o=="object"&&o!==null?o=Re(o):(l=ge(t)?Dt:ae.current,r=t.contextTypes,o=(r=r!=null)?pn(e,l):kt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ll,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function Zu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ll.enqueueReplaceState(t,t.state,null)}function qo(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Ui(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=Re(o):(o=ge(t)?Dt:ae.current,l.context=pn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Zo(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Ll.enqueueReplaceState(l,l.state,null),ml(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function gn(e,t){try{var n="",r=t;do n+=Rf(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function fo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function bo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var op=typeof WeakMap=="function"?WeakMap:Map;function vc(e,t,n){n=be(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){wl||(wl=!0,ai=r),bo(e,t)},n}function gc(e,t,n){n=be(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){bo(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){bo(e,t),typeof r!="function"&&(yt===null?yt=new Set([this]):yt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function qu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new op;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=wp.bind(null,e,t,n),t.then(e,e))}function bu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function es(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=be(-1,1),t.tag=2,gt(n,t,1))),n.lanes|=1),e)}var ip=lt.ReactCurrentOwner,he=!1;function ce(e,t,n,r){t.child=e===null?Ga(t,null,n,r):hn(t,e.child,n,r)}function ts(e,t,n,r,l){n=n.render;var o=t.ref;return cn(t,l),r=Wi(e,t,n,r,o,l),n=Gi(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,rt(e,t,l)):(K&&n&&Mi(t),t.flags|=1,ce(e,t,r,l),t.child)}function ns(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!nu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,yc(e,t,o,r,l)):(e=qr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:bn,n(i,r)&&e.ref===t.ref)return rt(e,t,l)}return t.flags|=1,e=St(o,r),e.ref=t.ref,e.return=t,t.child=e}function yc(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(bn(o,r)&&e.ref===t.ref)if(he=!1,t.pendingProps=r=o,(e.lanes&l)!==0)e.flags&131072&&(he=!0);else return t.lanes=e.lanes,rt(e,t,l)}return ei(e,t,n,r,l)}function wc(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},H(rn,Se),Se|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,H(rn,Se),Se|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,H(rn,Se),Se|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,H(rn,Se),Se|=r;return ce(e,t,l,n),t.child}function Sc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ei(e,t,n,r,l){var o=ge(n)?Dt:ae.current;return o=pn(t,o),cn(t,l),n=Wi(e,t,n,r,o,l),r=Gi(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,rt(e,t,l)):(K&&r&&Mi(t),t.flags|=1,ce(e,t,n,l),t.child)}function rs(e,t,n,r,l){if(ge(n)){var o=!0;al(t)}else o=!1;if(cn(t,l),t.stateNode===null)Xr(e,t),hc(t,n,r),qo(t,n,r,l),r=!0;else if(e===null){var i=t.stateNode,u=t.memoizedProps;i.props=u;var s=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=Re(c):(c=ge(n)?Dt:ae.current,c=pn(t,c));var v=n.getDerivedStateFromProps,h=typeof v=="function"||typeof i.getSnapshotBeforeUpdate=="function";h||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==r||s!==c)&&Zu(t,i,r,c),st=!1;var p=t.memoizedState;i.state=p,ml(t,r,i,l),s=t.memoizedState,u!==r||p!==s||ve.current||st?(typeof v=="function"&&(Zo(t,n,v,r),s=t.memoizedState),(u=st||Ju(t,n,u,r,p,s,c))?(h||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=c,r=u):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Ya(e,t),u=t.memoizedProps,c=t.type===t.elementType?u:ze(t.type,u),i.props=c,h=t.pendingProps,p=i.context,s=n.contextType,typeof s=="object"&&s!==null?s=Re(s):(s=ge(n)?Dt:ae.current,s=pn(t,s));var m=n.getDerivedStateFromProps;(v=typeof m=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==h||p!==s)&&Zu(t,i,r,s),st=!1,p=t.memoizedState,i.state=p,ml(t,r,i,l);var g=t.memoizedState;u!==h||p!==g||ve.current||st?(typeof m=="function"&&(Zo(t,n,m,r),g=t.memoizedState),(c=st||Ju(t,n,c,r,p,g,s)||!1)?(v||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,g,s),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,g,s)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),i.props=r,i.state=g,i.context=s,r=c):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return ti(e,t,n,r,o,l)}function ti(e,t,n,r,l,o){Sc(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return l&&Bu(t,n,!1),rt(e,t,o);r=t.stateNode,ip.current=t;var u=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=hn(t,e.child,null,o),t.child=hn(t,null,u,o)):ce(e,t,u,o),t.memoizedState=r.state,l&&Bu(t,n,!0),t.child}function Ec(e){var t=e.stateNode;t.pendingContext?Hu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Hu(e,t.context,!1),Hi(e,t.containerInfo)}function ls(e,t,n,r,l){return mn(),zi(l),t.flags|=256,ce(e,t,n,r),t.child}var ni={dehydrated:null,treeContext:null,retryLane:0};function ri(e){return{baseLanes:e,cachePool:null,transitions:null}}function xc(e,t,n){var r=t.pendingProps,l=W.current,o=!1,i=(t.flags&128)!==0,u;if((u=i)||(u=e!==null&&e.memoizedState===null?!1:(l&2)!==0),u?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),H(W,l&1),e===null)return Xo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Dl(i,r,0,null),e=At(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=ri(n),t.memoizedState=ni,e):Xi(t,i));if(l=e.memoizedState,l!==null&&(u=l.dehydrated,u!==null))return up(e,t,i,r,u,l,n);if(o){o=r.fallback,i=t.mode,l=e.child,u=l.sibling;var s={mode:"hidden",children:r.children};return!(i&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=s,t.deletions=null):(r=St(l,s),r.subtreeFlags=l.subtreeFlags&14680064),u!==null?o=St(u,o):(o=At(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?ri(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=ni,r}return o=e.child,e=o.sibling,r=St(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Xi(e,t){return t=Dl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function zr(e,t,n,r){return r!==null&&zi(r),hn(t,e.child,null,n),e=Xi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function up(e,t,n,r,l,o,i){if(n)return t.flags&256?(t.flags&=-257,r=fo(Error(S(422))),zr(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=Dl({mode:"visible",children:r.children},l,0,null),o=At(o,l,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&hn(t,e.child,null,i),t.child.memoizedState=ri(i),t.memoizedState=ni,o);if(!(t.mode&1))return zr(e,t,i,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var u=r.dgst;return r=u,o=Error(S(419)),r=fo(o,r,void 0),zr(e,t,i,r)}if(u=(i&e.childLanes)!==0,he||u){if(r=ne,r!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,nt(e,l),$e(r,e,l,-1))}return tu(),r=fo(Error(S(421))),zr(e,t,i,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Sp.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,xe=vt(l.nextSibling),ke=t,K=!0,De=null,e!==null&&(Oe[Te++]=Ze,Oe[Te++]=qe,Oe[Te++]=Ft,Ze=e.id,qe=e.overflow,Ft=t),t=Xi(t,r.children),t.flags|=4096,t)}function os(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Jo(e.return,t,n)}function po(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function kc(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(ce(e,t,r.children,n),r=W.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&os(e,n,t);else if(e.tag===19)os(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(H(W,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&hl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),po(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&hl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}po(t,!0,n,null,o);break;case"together":po(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function rt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ut|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,n=St(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=St(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function sp(e,t,n){switch(t.tag){case 3:Ec(t),mn();break;case 5:Xa(t);break;case 1:ge(t.type)&&al(t);break;case 4:Hi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;H(dl,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(H(W,W.current&1),t.flags|=128,null):n&t.child.childLanes?xc(e,t,n):(H(W,W.current&1),e=rt(e,t,n),e!==null?e.sibling:null);H(W,W.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return kc(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),H(W,W.current),r)break;return null;case 22:case 23:return t.lanes=0,wc(e,t,n)}return rt(e,t,n)}var Cc,li,_c,Nc;Cc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};li=function(){};_c=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Mt(Ye.current);var o=null;switch(n){case"input":l=No(e,l),r=No(e,r),o=[];break;case"select":l=Q({},l,{value:void 0}),r=Q({},r,{value:void 0}),o=[];break;case"textarea":l=To(e,l),r=To(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ul)}Io(n,r);var i;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var u=l[c];for(i in u)u.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Gn.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var s=r[c];if(u=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&s!==u&&(s!=null||u!=null))if(c==="style")if(u){for(i in u)!u.hasOwnProperty(i)||s&&s.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in s)s.hasOwnProperty(i)&&u[i]!==s[i]&&(n||(n={}),n[i]=s[i])}else n||(o||(o=[]),o.push(c,n)),n=s;else c==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,u=u?u.__html:void 0,s!=null&&u!==s&&(o=o||[]).push(c,s)):c==="children"?typeof s!="string"&&typeof s!="number"||(o=o||[]).push(c,""+s):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Gn.hasOwnProperty(c)?(s!=null&&c==="onScroll"&&B("scroll",e),o||u===s||(o=[])):(o=o||[]).push(c,s))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};Nc=function(e,t,n,r){n!==r&&(t.flags|=4)};function On(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ue(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ap(e,t,n){var r=t.pendingProps;switch(Li(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ue(t),null;case 1:return ge(t.type)&&sl(),ue(t),null;case 3:return r=t.stateNode,vn(),V(ve),V(ae),Vi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Mr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,De!==null&&(di(De),De=null))),li(e,t),ue(t),null;case 5:Bi(t);var l=Mt(lr.current);if(n=t.type,e!==null&&t.stateNode!=null)_c(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(S(166));return ue(t),null}if(e=Mt(Ye.current),Mr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[Ge]=t,r[nr]=o,e=(t.mode&1)!==0,n){case"dialog":B("cancel",r),B("close",r);break;case"iframe":case"object":case"embed":B("load",r);break;case"video":case"audio":for(l=0;l<Mn.length;l++)B(Mn[l],r);break;case"source":B("error",r);break;case"img":case"image":case"link":B("error",r),B("load",r);break;case"details":B("toggle",r);break;case"input":mu(r,o),B("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},B("invalid",r);break;case"textarea":vu(r,o),B("invalid",r)}Io(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var u=o[i];i==="children"?typeof u=="string"?r.textContent!==u&&(o.suppressHydrationWarning!==!0&&Rr(r.textContent,u,e),l=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(o.suppressHydrationWarning!==!0&&Rr(r.textContent,u,e),l=["children",""+u]):Gn.hasOwnProperty(i)&&u!=null&&i==="onScroll"&&B("scroll",r)}switch(n){case"input":Cr(r),hu(r,o,!0);break;case"textarea":Cr(r),gu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=ul)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ea(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Ge]=t,e[nr]=r,Cc(e,t,!1,!1),t.stateNode=e;e:{switch(i=Ro(n,r),n){case"dialog":B("cancel",e),B("close",e),l=r;break;case"iframe":case"object":case"embed":B("load",e),l=r;break;case"video":case"audio":for(l=0;l<Mn.length;l++)B(Mn[l],e);l=r;break;case"source":B("error",e),l=r;break;case"img":case"image":case"link":B("error",e),B("load",e),l=r;break;case"details":B("toggle",e),l=r;break;case"input":mu(e,r),l=No(e,r),B("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=Q({},r,{value:void 0}),B("invalid",e);break;case"textarea":vu(e,r),l=To(e,r),B("invalid",e);break;default:l=r}Io(n,l),u=l;for(o in u)if(u.hasOwnProperty(o)){var s=u[o];o==="style"?ra(e,s):o==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,s!=null&&ta(e,s)):o==="children"?typeof s=="string"?(n!=="textarea"||s!=="")&&Qn(e,s):typeof s=="number"&&Qn(e,""+s):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Gn.hasOwnProperty(o)?s!=null&&o==="onScroll"&&B("scroll",e):s!=null&&wi(e,o,s,i))}switch(n){case"input":Cr(e),hu(e,r,!1);break;case"textarea":Cr(e),gu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+xt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?on(e,!!r.multiple,o,!1):r.defaultValue!=null&&on(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=ul)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ue(t),null;case 6:if(e&&t.stateNode!=null)Nc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(S(166));if(n=Mt(lr.current),Mt(Ye.current),Mr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ge]=t,(o=r.nodeValue!==n)&&(e=ke,e!==null))switch(e.tag){case 3:Rr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Rr(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ge]=t,t.stateNode=r}return ue(t),null;case 13:if(V(W),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(K&&xe!==null&&t.mode&1&&!(t.flags&128))Ka(),mn(),t.flags|=98560,o=!1;else if(o=Mr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(S(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(S(317));o[Ge]=t}else mn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ue(t),o=!1}else De!==null&&(di(De),De=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||W.current&1?q===0&&(q=3):tu())),t.updateQueue!==null&&(t.flags|=4),ue(t),null);case 4:return vn(),li(e,t),e===null&&er(t.stateNode.containerInfo),ue(t),null;case 10:return Fi(t.type._context),ue(t),null;case 17:return ge(t.type)&&sl(),ue(t),null;case 19:if(V(W),o=t.memoizedState,o===null)return ue(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)On(o,!1);else{if(q!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=hl(e),i!==null){for(t.flags|=128,On(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return H(W,W.current&1|2),t.child}e=e.sibling}o.tail!==null&&X()>yn&&(t.flags|=128,r=!0,On(o,!1),t.lanes=4194304)}else{if(!r)if(e=hl(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),On(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!K)return ue(t),null}else 2*X()-o.renderingStartTime>yn&&n!==1073741824&&(t.flags|=128,r=!0,On(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=X(),t.sibling=null,n=W.current,H(W,r?n&1|2:n&1),t):(ue(t),null);case 22:case 23:return eu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Se&1073741824&&(ue(t),t.subtreeFlags&6&&(t.flags|=8192)):ue(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function cp(e,t){switch(Li(t),t.tag){case 1:return ge(t.type)&&sl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return vn(),V(ve),V(ae),Vi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Bi(t),null;case 13:if(V(W),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));mn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(W),null;case 4:return vn(),null;case 10:return Fi(t.type._context),null;case 22:case 23:return eu(),null;case 24:return null;default:return null}}var Ar=!1,se=!1,fp=typeof WeakSet=="function"?WeakSet:Set,N=null;function nn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Y(e,t,r)}else n.current=null}function oi(e,t,n){try{n()}catch(r){Y(e,t,r)}}var is=!1;function dp(e,t){if(Bo=ll,e=Ia(),Ri(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,u=-1,s=-1,c=0,v=0,h=e,p=null;t:for(;;){for(var m;h!==n||l!==0&&h.nodeType!==3||(u=i+l),h!==o||r!==0&&h.nodeType!==3||(s=i+r),h.nodeType===3&&(i+=h.nodeValue.length),(m=h.firstChild)!==null;)p=h,h=m;for(;;){if(h===e)break t;if(p===n&&++c===l&&(u=i),p===o&&++v===r&&(s=i),(m=h.nextSibling)!==null)break;h=p,p=h.parentNode}h=m}n=u===-1||s===-1?null:{start:u,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(Vo={focusedElem:e,selectionRange:n},ll=!1,N=t;N!==null;)if(t=N,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,N=e;else for(;N!==null;){t=N;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var E=g.memoizedProps,$=g.memoizedState,f=t.stateNode,a=f.getSnapshotBeforeUpdate(t.elementType===t.type?E:ze(t.type,E),$);f.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var d=t.stateNode.containerInfo;d.nodeType===1?d.textContent="":d.nodeType===9&&d.documentElement&&d.removeChild(d.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(y){Y(t,t.return,y)}if(e=t.sibling,e!==null){e.return=t.return,N=e;break}N=t.return}return g=is,is=!1,g}function Hn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&oi(t,n,o)}l=l.next}while(l!==r)}}function zl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ii(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Pc(e){var t=e.alternate;t!==null&&(e.alternate=null,Pc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ge],delete t[nr],delete t[Go],delete t[Yd],delete t[Xd])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Oc(e){return e.tag===5||e.tag===3||e.tag===4}function us(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Oc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ui(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ul));else if(r!==4&&(e=e.child,e!==null))for(ui(e,t,n),e=e.sibling;e!==null;)ui(e,t,n),e=e.sibling}function si(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(si(e,t,n),e=e.sibling;e!==null;)si(e,t,n),e=e.sibling}var re=null,Ae=!1;function it(e,t,n){for(n=n.child;n!==null;)Tc(e,t,n),n=n.sibling}function Tc(e,t,n){if(Qe&&typeof Qe.onCommitFiberUnmount=="function")try{Qe.onCommitFiberUnmount(Pl,n)}catch{}switch(n.tag){case 5:se||nn(n,t);case 6:var r=re,l=Ae;re=null,it(e,t,n),re=r,Ae=l,re!==null&&(Ae?(e=re,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):re.removeChild(n.stateNode));break;case 18:re!==null&&(Ae?(e=re,n=n.stateNode,e.nodeType===8?oo(e.parentNode,n):e.nodeType===1&&oo(e,n),Zn(e)):oo(re,n.stateNode));break;case 4:r=re,l=Ae,re=n.stateNode.containerInfo,Ae=!0,it(e,t,n),re=r,Ae=l;break;case 0:case 11:case 14:case 15:if(!se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&oi(n,t,i),l=l.next}while(l!==r)}it(e,t,n);break;case 1:if(!se&&(nn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(u){Y(n,t,u)}it(e,t,n);break;case 21:it(e,t,n);break;case 22:n.mode&1?(se=(r=se)||n.memoizedState!==null,it(e,t,n),se=r):it(e,t,n);break;default:it(e,t,n)}}function ss(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new fp),t.forEach(function(r){var l=Ep.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Le(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,i=t,u=i;e:for(;u!==null;){switch(u.tag){case 5:re=u.stateNode,Ae=!1;break e;case 3:re=u.stateNode.containerInfo,Ae=!0;break e;case 4:re=u.stateNode.containerInfo,Ae=!0;break e}u=u.return}if(re===null)throw Error(S(160));Tc(o,i,l),re=null,Ae=!1;var s=l.alternate;s!==null&&(s.return=null),l.return=null}catch(c){Y(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)jc(t,e),t=t.sibling}function jc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Le(t,e),Ke(e),r&4){try{Hn(3,e,e.return),zl(3,e)}catch(E){Y(e,e.return,E)}try{Hn(5,e,e.return)}catch(E){Y(e,e.return,E)}}break;case 1:Le(t,e),Ke(e),r&512&&n!==null&&nn(n,n.return);break;case 5:if(Le(t,e),Ke(e),r&512&&n!==null&&nn(n,n.return),e.flags&32){var l=e.stateNode;try{Qn(l,"")}catch(E){Y(e,e.return,E)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,u=e.type,s=e.updateQueue;if(e.updateQueue=null,s!==null)try{u==="input"&&o.type==="radio"&&o.name!=null&&qs(l,o),Ro(u,i);var c=Ro(u,o);for(i=0;i<s.length;i+=2){var v=s[i],h=s[i+1];v==="style"?ra(l,h):v==="dangerouslySetInnerHTML"?ta(l,h):v==="children"?Qn(l,h):wi(l,v,h,c)}switch(u){case"input":Po(l,o);break;case"textarea":bs(l,o);break;case"select":var p=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var m=o.value;m!=null?on(l,!!o.multiple,m,!1):p!==!!o.multiple&&(o.defaultValue!=null?on(l,!!o.multiple,o.defaultValue,!0):on(l,!!o.multiple,o.multiple?[]:"",!1))}l[nr]=o}catch(E){Y(e,e.return,E)}}break;case 6:if(Le(t,e),Ke(e),r&4){if(e.stateNode===null)throw Error(S(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(E){Y(e,e.return,E)}}break;case 3:if(Le(t,e),Ke(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Zn(t.containerInfo)}catch(E){Y(e,e.return,E)}break;case 4:Le(t,e),Ke(e);break;case 13:Le(t,e),Ke(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(qi=X())),r&4&&ss(e);break;case 22:if(v=n!==null&&n.memoizedState!==null,e.mode&1?(se=(c=se)||v,Le(t,e),se=c):Le(t,e),Ke(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!v&&e.mode&1)for(N=e,v=e.child;v!==null;){for(h=N=v;N!==null;){switch(p=N,m=p.child,p.tag){case 0:case 11:case 14:case 15:Hn(4,p,p.return);break;case 1:nn(p,p.return);var g=p.stateNode;if(typeof g.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(E){Y(r,n,E)}}break;case 5:nn(p,p.return);break;case 22:if(p.memoizedState!==null){cs(h);continue}}m!==null?(m.return=p,N=m):cs(h)}v=v.sibling}e:for(v=null,h=e;;){if(h.tag===5){if(v===null){v=h;try{l=h.stateNode,c?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(u=h.stateNode,s=h.memoizedProps.style,i=s!=null&&s.hasOwnProperty("display")?s.display:null,u.style.display=na("display",i))}catch(E){Y(e,e.return,E)}}}else if(h.tag===6){if(v===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(E){Y(e,e.return,E)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;v===h&&(v=null),h=h.return}v===h&&(v=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Le(t,e),Ke(e),r&4&&ss(e);break;case 21:break;default:Le(t,e),Ke(e)}}function Ke(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Oc(n)){var r=n;break e}n=n.return}throw Error(S(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Qn(l,""),r.flags&=-33);var o=us(e);si(e,o,l);break;case 3:case 4:var i=r.stateNode.containerInfo,u=us(e);ui(e,u,i);break;default:throw Error(S(161))}}catch(s){Y(e,e.return,s)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function pp(e,t,n){N=e,Ic(e)}function Ic(e,t,n){for(var r=(e.mode&1)!==0;N!==null;){var l=N,o=l.child;if(l.tag===22&&r){var i=l.memoizedState!==null||Ar;if(!i){var u=l.alternate,s=u!==null&&u.memoizedState!==null||se;u=Ar;var c=se;if(Ar=i,(se=s)&&!c)for(N=l;N!==null;)i=N,s=i.child,i.tag===22&&i.memoizedState!==null?fs(l):s!==null?(s.return=i,N=s):fs(l);for(;o!==null;)N=o,Ic(o),o=o.sibling;N=l,Ar=u,se=c}as(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,N=o):as(e)}}function as(e){for(;N!==null;){var t=N;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:se||zl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!se)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ze(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Qu(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Qu(t,i,n)}break;case 5:var u=t.stateNode;if(n===null&&t.flags&4){n=u;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var v=c.memoizedState;if(v!==null){var h=v.dehydrated;h!==null&&Zn(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}se||t.flags&512&&ii(t)}catch(p){Y(t,t.return,p)}}if(t===e){N=null;break}if(n=t.sibling,n!==null){n.return=t.return,N=n;break}N=t.return}}function cs(e){for(;N!==null;){var t=N;if(t===e){N=null;break}var n=t.sibling;if(n!==null){n.return=t.return,N=n;break}N=t.return}}function fs(e){for(;N!==null;){var t=N;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{zl(4,t)}catch(s){Y(t,n,s)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(s){Y(t,l,s)}}var o=t.return;try{ii(t)}catch(s){Y(t,o,s)}break;case 5:var i=t.return;try{ii(t)}catch(s){Y(t,i,s)}}}catch(s){Y(t,t.return,s)}if(t===e){N=null;break}var u=t.sibling;if(u!==null){u.return=t.return,N=u;break}N=t.return}}var mp=Math.ceil,yl=lt.ReactCurrentDispatcher,Ji=lt.ReactCurrentOwner,Ie=lt.ReactCurrentBatchConfig,D=0,ne=null,J=null,le=0,Se=0,rn=_t(0),q=0,sr=null,Ut=0,Al=0,Zi=0,Bn=null,me=null,qi=0,yn=1/0,Xe=null,wl=!1,ai=null,yt=null,Dr=!1,dt=null,Sl=0,Vn=0,ci=null,Jr=-1,Zr=0;function fe(){return D&6?X():Jr!==-1?Jr:Jr=X()}function wt(e){return e.mode&1?D&2&&le!==0?le&-le:Zd.transition!==null?(Zr===0&&(Zr=ha()),Zr):(e=F,e!==0||(e=window.event,e=e===void 0?16:xa(e.type)),e):1}function $e(e,t,n,r){if(50<Vn)throw Vn=0,ci=null,Error(S(185));pr(e,n,r),(!(D&2)||e!==ne)&&(e===ne&&(!(D&2)&&(Al|=n),q===4&&ct(e,le)),ye(e,r),n===1&&D===0&&!(t.mode&1)&&(yn=X()+500,Rl&&Nt()))}function ye(e,t){var n=e.callbackNode;Zf(e,t);var r=rl(e,e===ne?le:0);if(r===0)n!==null&&Su(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Su(n),t===1)e.tag===0?Jd(ds.bind(null,e)):Ha(ds.bind(null,e)),Gd(function(){!(D&6)&&Nt()}),n=null;else{switch(va(r)){case 1:n=Ci;break;case 4:n=pa;break;case 16:n=nl;break;case 536870912:n=ma;break;default:n=nl}n=$c(n,Rc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Rc(e,t){if(Jr=-1,Zr=0,D&6)throw Error(S(327));var n=e.callbackNode;if(fn()&&e.callbackNode!==n)return null;var r=rl(e,e===ne?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=El(e,r);else{t=r;var l=D;D|=2;var o=Lc();(ne!==e||le!==t)&&(Xe=null,yn=X()+500,zt(e,t));do try{gp();break}catch(u){Mc(e,u)}while(!0);Di(),yl.current=o,D=l,J!==null?t=0:(ne=null,le=0,t=q)}if(t!==0){if(t===2&&(l=Do(e),l!==0&&(r=l,t=fi(e,l))),t===1)throw n=sr,zt(e,0),ct(e,r),ye(e,X()),n;if(t===6)ct(e,r);else{if(l=e.current.alternate,!(r&30)&&!hp(l)&&(t=El(e,r),t===2&&(o=Do(e),o!==0&&(r=o,t=fi(e,o))),t===1))throw n=sr,zt(e,0),ct(e,r),ye(e,X()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(S(345));case 2:Tt(e,me,Xe);break;case 3:if(ct(e,r),(r&130023424)===r&&(t=qi+500-X(),10<t)){if(rl(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){fe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Wo(Tt.bind(null,e,me,Xe),t);break}Tt(e,me,Xe);break;case 4:if(ct(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var i=31-Fe(r);o=1<<i,i=t[i],i>l&&(l=i),r&=~o}if(r=l,r=X()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*mp(r/1960))-r,10<r){e.timeoutHandle=Wo(Tt.bind(null,e,me,Xe),r);break}Tt(e,me,Xe);break;case 5:Tt(e,me,Xe);break;default:throw Error(S(329))}}}return ye(e,X()),e.callbackNode===n?Rc.bind(null,e):null}function fi(e,t){var n=Bn;return e.current.memoizedState.isDehydrated&&(zt(e,t).flags|=256),e=El(e,t),e!==2&&(t=me,me=n,t!==null&&di(t)),e}function di(e){me===null?me=e:me.push.apply(me,e)}function hp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!Ue(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ct(e,t){for(t&=~Zi,t&=~Al,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Fe(t),r=1<<n;e[n]=-1,t&=~r}}function ds(e){if(D&6)throw Error(S(327));fn();var t=rl(e,0);if(!(t&1))return ye(e,X()),null;var n=El(e,t);if(e.tag!==0&&n===2){var r=Do(e);r!==0&&(t=r,n=fi(e,r))}if(n===1)throw n=sr,zt(e,0),ct(e,t),ye(e,X()),n;if(n===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Tt(e,me,Xe),ye(e,X()),null}function bi(e,t){var n=D;D|=1;try{return e(t)}finally{D=n,D===0&&(yn=X()+500,Rl&&Nt())}}function Ht(e){dt!==null&&dt.tag===0&&!(D&6)&&fn();var t=D;D|=1;var n=Ie.transition,r=F;try{if(Ie.transition=null,F=1,e)return e()}finally{F=r,Ie.transition=n,D=t,!(D&6)&&Nt()}}function eu(){Se=rn.current,V(rn)}function zt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Wd(n)),J!==null)for(n=J.return;n!==null;){var r=n;switch(Li(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&sl();break;case 3:vn(),V(ve),V(ae),Vi();break;case 5:Bi(r);break;case 4:vn();break;case 13:V(W);break;case 19:V(W);break;case 10:Fi(r.type._context);break;case 22:case 23:eu()}n=n.return}if(ne=e,J=e=St(e.current,null),le=Se=t,q=0,sr=null,Zi=Al=Ut=0,me=Bn=null,Rt!==null){for(t=0;t<Rt.length;t++)if(n=Rt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,r.next=i}n.pending=r}Rt=null}return e}function Mc(e,t){do{var n=J;try{if(Di(),Qr.current=gl,vl){for(var r=G.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}vl=!1}if($t=0,te=Z=G=null,Un=!1,or=0,Ji.current=null,n===null||n.return===null){q=1,sr=t,J=null;break}e:{var o=e,i=n.return,u=n,s=t;if(t=le,u.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){var c=s,v=u,h=v.tag;if(!(v.mode&1)&&(h===0||h===11||h===15)){var p=v.alternate;p?(v.updateQueue=p.updateQueue,v.memoizedState=p.memoizedState,v.lanes=p.lanes):(v.updateQueue=null,v.memoizedState=null)}var m=bu(i);if(m!==null){m.flags&=-257,es(m,i,u,o,t),m.mode&1&&qu(o,c,t),t=m,s=c;var g=t.updateQueue;if(g===null){var E=new Set;E.add(s),t.updateQueue=E}else g.add(s);break e}else{if(!(t&1)){qu(o,c,t),tu();break e}s=Error(S(426))}}else if(K&&u.mode&1){var $=bu(i);if($!==null){!($.flags&65536)&&($.flags|=256),es($,i,u,o,t),zi(gn(s,u));break e}}o=s=gn(s,u),q!==4&&(q=2),Bn===null?Bn=[o]:Bn.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var f=vc(o,s,t);Gu(o,f);break e;case 1:u=s;var a=o.type,d=o.stateNode;if(!(o.flags&128)&&(typeof a.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(yt===null||!yt.has(d)))){o.flags|=65536,t&=-t,o.lanes|=t;var y=gc(o,u,t);Gu(o,y);break e}}o=o.return}while(o!==null)}Ac(n)}catch(x){t=x,J===n&&n!==null&&(J=n=n.return);continue}break}while(!0)}function Lc(){var e=yl.current;return yl.current=gl,e===null?gl:e}function tu(){(q===0||q===3||q===2)&&(q=4),ne===null||!(Ut&268435455)&&!(Al&268435455)||ct(ne,le)}function El(e,t){var n=D;D|=2;var r=Lc();(ne!==e||le!==t)&&(Xe=null,zt(e,t));do try{vp();break}catch(l){Mc(e,l)}while(!0);if(Di(),D=n,yl.current=r,J!==null)throw Error(S(261));return ne=null,le=0,q}function vp(){for(;J!==null;)zc(J)}function gp(){for(;J!==null&&!Bf();)zc(J)}function zc(e){var t=Fc(e.alternate,e,Se);e.memoizedProps=e.pendingProps,t===null?Ac(e):J=t,Ji.current=null}function Ac(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=cp(n,t),n!==null){n.flags&=32767,J=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{q=6,J=null;return}}else if(n=ap(n,t,Se),n!==null){J=n;return}if(t=t.sibling,t!==null){J=t;return}J=t=e}while(t!==null);q===0&&(q=5)}function Tt(e,t,n){var r=F,l=Ie.transition;try{Ie.transition=null,F=1,yp(e,t,n,r)}finally{Ie.transition=l,F=r}return null}function yp(e,t,n,r){do fn();while(dt!==null);if(D&6)throw Error(S(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(qf(e,o),e===ne&&(J=ne=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Dr||(Dr=!0,$c(nl,function(){return fn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Ie.transition,Ie.transition=null;var i=F;F=1;var u=D;D|=4,Ji.current=null,dp(e,n),jc(n,e),Fd(Vo),ll=!!Bo,Vo=Bo=null,e.current=n,pp(n),Vf(),D=u,F=i,Ie.transition=o}else e.current=n;if(Dr&&(Dr=!1,dt=e,Sl=l),o=e.pendingLanes,o===0&&(yt=null),Gf(n.stateNode),ye(e,X()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(wl)throw wl=!1,e=ai,ai=null,e;return Sl&1&&e.tag!==0&&fn(),o=e.pendingLanes,o&1?e===ci?Vn++:(Vn=0,ci=e):Vn=0,Nt(),null}function fn(){if(dt!==null){var e=va(Sl),t=Ie.transition,n=F;try{if(Ie.transition=null,F=16>e?16:e,dt===null)var r=!1;else{if(e=dt,dt=null,Sl=0,D&6)throw Error(S(331));var l=D;for(D|=4,N=e.current;N!==null;){var o=N,i=o.child;if(N.flags&16){var u=o.deletions;if(u!==null){for(var s=0;s<u.length;s++){var c=u[s];for(N=c;N!==null;){var v=N;switch(v.tag){case 0:case 11:case 15:Hn(8,v,o)}var h=v.child;if(h!==null)h.return=v,N=h;else for(;N!==null;){v=N;var p=v.sibling,m=v.return;if(Pc(v),v===c){N=null;break}if(p!==null){p.return=m,N=p;break}N=m}}}var g=o.alternate;if(g!==null){var E=g.child;if(E!==null){g.child=null;do{var $=E.sibling;E.sibling=null,E=$}while(E!==null)}}N=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,N=i;else e:for(;N!==null;){if(o=N,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Hn(9,o,o.return)}var f=o.sibling;if(f!==null){f.return=o.return,N=f;break e}N=o.return}}var a=e.current;for(N=a;N!==null;){i=N;var d=i.child;if(i.subtreeFlags&2064&&d!==null)d.return=i,N=d;else e:for(i=a;N!==null;){if(u=N,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:zl(9,u)}}catch(x){Y(u,u.return,x)}if(u===i){N=null;break e}var y=u.sibling;if(y!==null){y.return=u.return,N=y;break e}N=u.return}}if(D=l,Nt(),Qe&&typeof Qe.onPostCommitFiberRoot=="function")try{Qe.onPostCommitFiberRoot(Pl,e)}catch{}r=!0}return r}finally{F=n,Ie.transition=t}}return!1}function ps(e,t,n){t=gn(n,t),t=vc(e,t,1),e=gt(e,t,1),t=fe(),e!==null&&(pr(e,1,t),ye(e,t))}function Y(e,t,n){if(e.tag===3)ps(e,e,n);else for(;t!==null;){if(t.tag===3){ps(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(yt===null||!yt.has(r))){e=gn(n,e),e=gc(t,e,1),t=gt(t,e,1),e=fe(),t!==null&&(pr(t,1,e),ye(t,e));break}}t=t.return}}function wp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=fe(),e.pingedLanes|=e.suspendedLanes&n,ne===e&&(le&n)===n&&(q===4||q===3&&(le&130023424)===le&&500>X()-qi?zt(e,0):Zi|=n),ye(e,t)}function Dc(e,t){t===0&&(e.mode&1?(t=Pr,Pr<<=1,!(Pr&130023424)&&(Pr=4194304)):t=1);var n=fe();e=nt(e,t),e!==null&&(pr(e,t,n),ye(e,n))}function Sp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Dc(e,n)}function Ep(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(S(314))}r!==null&&r.delete(t),Dc(e,n)}var Fc;Fc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ve.current)he=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return he=!1,sp(e,t,n);he=!!(e.flags&131072)}else he=!1,K&&t.flags&1048576&&Ba(t,fl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Xr(e,t),e=t.pendingProps;var l=pn(t,ae.current);cn(t,n),l=Wi(null,t,r,e,l,n);var o=Gi();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ge(r)?(o=!0,al(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Ui(t),l.updater=Ll,t.stateNode=l,l._reactInternals=t,qo(t,r,e,n),t=ti(null,t,r,!0,o,n)):(t.tag=0,K&&o&&Mi(t),ce(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Xr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=kp(r),e=ze(r,e),l){case 0:t=ei(null,t,r,e,n);break e;case 1:t=rs(null,t,r,e,n);break e;case 11:t=ts(null,t,r,e,n);break e;case 14:t=ns(null,t,r,ze(r.type,e),n);break e}throw Error(S(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),ei(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),rs(e,t,r,l,n);case 3:e:{if(Ec(t),e===null)throw Error(S(387));r=t.pendingProps,o=t.memoizedState,l=o.element,Ya(e,t),ml(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=gn(Error(S(423)),t),t=ls(e,t,r,n,l);break e}else if(r!==l){l=gn(Error(S(424)),t),t=ls(e,t,r,n,l);break e}else for(xe=vt(t.stateNode.containerInfo.firstChild),ke=t,K=!0,De=null,n=Ga(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(mn(),r===l){t=rt(e,t,n);break e}ce(e,t,r,n)}t=t.child}return t;case 5:return Xa(t),e===null&&Xo(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,Ko(r,l)?i=null:o!==null&&Ko(r,o)&&(t.flags|=32),Sc(e,t),ce(e,t,i,n),t.child;case 6:return e===null&&Xo(t),null;case 13:return xc(e,t,n);case 4:return Hi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hn(t,null,r,n):ce(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),ts(e,t,r,l,n);case 7:return ce(e,t,t.pendingProps,n),t.child;case 8:return ce(e,t,t.pendingProps.children,n),t.child;case 12:return ce(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,H(dl,r._currentValue),r._currentValue=i,o!==null)if(Ue(o.value,i)){if(o.children===l.children&&!ve.current){t=rt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){i=o.child;for(var s=u.firstContext;s!==null;){if(s.context===r){if(o.tag===1){s=be(-1,n&-n),s.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var v=c.pending;v===null?s.next=s:(s.next=v.next,v.next=s),c.pending=s}}o.lanes|=n,s=o.alternate,s!==null&&(s.lanes|=n),Jo(o.return,n,t),u.lanes|=n;break}s=s.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(S(341));i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),Jo(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}ce(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,cn(t,n),l=Re(l),r=r(l),t.flags|=1,ce(e,t,r,n),t.child;case 14:return r=t.type,l=ze(r,t.pendingProps),l=ze(r.type,l),ns(e,t,r,l,n);case 15:return yc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ze(r,l),Xr(e,t),t.tag=1,ge(r)?(e=!0,al(t)):e=!1,cn(t,n),hc(t,r,l),qo(t,r,l,n),ti(null,t,r,!0,e,n);case 19:return kc(e,t,n);case 22:return wc(e,t,n)}throw Error(S(156,t.tag))};function $c(e,t){return da(e,t)}function xp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function je(e,t,n,r){return new xp(e,t,n,r)}function nu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function kp(e){if(typeof e=="function")return nu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ei)return 11;if(e===xi)return 14}return 2}function St(e,t){var n=e.alternate;return n===null?(n=je(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function qr(e,t,n,r,l,o){var i=2;if(r=e,typeof e=="function")nu(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Qt:return At(n.children,l,o,t);case Si:i=8,l|=8;break;case xo:return e=je(12,n,t,l|2),e.elementType=xo,e.lanes=o,e;case ko:return e=je(13,n,t,l),e.elementType=ko,e.lanes=o,e;case Co:return e=je(19,n,t,l),e.elementType=Co,e.lanes=o,e;case Xs:return Dl(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Qs:i=10;break e;case Ys:i=9;break e;case Ei:i=11;break e;case xi:i=14;break e;case ut:i=16,r=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=je(i,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function At(e,t,n,r){return e=je(7,e,r,t),e.lanes=n,e}function Dl(e,t,n,r){return e=je(22,e,r,t),e.elementType=Xs,e.lanes=n,e.stateNode={isHidden:!1},e}function mo(e,t,n){return e=je(6,e,null,t),e.lanes=n,e}function ho(e,t,n){return t=je(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Cp(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Yl(0),this.expirationTimes=Yl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yl(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function ru(e,t,n,r,l,o,i,u,s){return e=new Cp(e,t,n,u,s),t===1?(t=1,o===!0&&(t|=8)):t=0,o=je(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ui(o),e}function _p(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Gt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Uc(e){if(!e)return kt;e=e._reactInternals;e:{if(Kt(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ge(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var n=e.type;if(ge(n))return Ua(e,n,t)}return t}function Hc(e,t,n,r,l,o,i,u,s){return e=ru(n,r,!0,e,l,o,i,u,s),e.context=Uc(null),n=e.current,r=fe(),l=wt(n),o=be(r,l),o.callback=t??null,gt(n,o,l),e.current.lanes=l,pr(e,l,r),ye(e,r),e}function Fl(e,t,n,r){var l=t.current,o=fe(),i=wt(l);return n=Uc(n),t.context===null?t.context=n:t.pendingContext=n,t=be(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=gt(l,t,i),e!==null&&($e(e,l,i,o),Gr(e,l,i)),i}function xl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ms(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function lu(e,t){ms(e,t),(e=e.alternate)&&ms(e,t)}function Np(){return null}var Bc=typeof reportError=="function"?reportError:function(e){console.error(e)};function ou(e){this._internalRoot=e}$l.prototype.render=ou.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));Fl(e,t,null,null)};$l.prototype.unmount=ou.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ht(function(){Fl(null,e,null,null)}),t[tt]=null}};function $l(e){this._internalRoot=e}$l.prototype.unstable_scheduleHydration=function(e){if(e){var t=wa();e={blockedOn:null,target:e,priority:t};for(var n=0;n<at.length&&t!==0&&t<at[n].priority;n++);at.splice(n,0,e),n===0&&Ea(e)}};function iu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ul(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function hs(){}function Pp(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var c=xl(i);o.call(c)}}var i=Hc(t,r,e,0,null,!1,!1,"",hs);return e._reactRootContainer=i,e[tt]=i.current,er(e.nodeType===8?e.parentNode:e),Ht(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var u=r;r=function(){var c=xl(s);u.call(c)}}var s=ru(e,0,!1,null,null,!1,!1,"",hs);return e._reactRootContainer=s,e[tt]=s.current,er(e.nodeType===8?e.parentNode:e),Ht(function(){Fl(t,s,n,r)}),s}function Hl(e,t,n,r,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var u=l;l=function(){var s=xl(i);u.call(s)}}Fl(t,i,e,l)}else i=Pp(n,t,e,l,r);return xl(i)}ga=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Rn(t.pendingLanes);n!==0&&(_i(t,n|1),ye(t,X()),!(D&6)&&(yn=X()+500,Nt()))}break;case 13:Ht(function(){var r=nt(e,1);if(r!==null){var l=fe();$e(r,e,1,l)}}),lu(e,1)}};Ni=function(e){if(e.tag===13){var t=nt(e,134217728);if(t!==null){var n=fe();$e(t,e,134217728,n)}lu(e,134217728)}};ya=function(e){if(e.tag===13){var t=wt(e),n=nt(e,t);if(n!==null){var r=fe();$e(n,e,t,r)}lu(e,t)}};wa=function(){return F};Sa=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Lo=function(e,t,n){switch(t){case"input":if(Po(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Il(r);if(!l)throw Error(S(90));Zs(r),Po(r,l)}}}break;case"textarea":bs(e,n);break;case"select":t=n.value,t!=null&&on(e,!!n.multiple,t,!1)}};ia=bi;ua=Ht;var Op={usingClientEntryPoint:!1,Events:[hr,Zt,Il,la,oa,bi]},Tn={findFiberByHostInstance:It,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Tp={bundleType:Tn.bundleType,version:Tn.version,rendererPackageName:Tn.rendererPackageName,rendererConfig:Tn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:lt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ca(e),e===null?null:e.stateNode},findFiberByHostInstance:Tn.findFiberByHostInstance||Np,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Fr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Fr.isDisabled&&Fr.supportsFiber)try{Pl=Fr.inject(Tp),Qe=Fr}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Op;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!iu(t))throw Error(S(200));return _p(e,t,null,n)};_e.createRoot=function(e,t){if(!iu(e))throw Error(S(299));var n=!1,r="",l=Bc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=ru(e,1,!1,null,null,n,!1,r,l),e[tt]=t.current,er(e.nodeType===8?e.parentNode:e),new ou(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=ca(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return Ht(e)};_e.hydrate=function(e,t,n){if(!Ul(t))throw Error(S(200));return Hl(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!iu(e))throw Error(S(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",i=Bc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Hc(t,null,e,1,n??null,l,!1,o,i),e[tt]=t.current,er(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new $l(t)};_e.render=function(e,t,n){if(!Ul(t))throw Error(S(200));return Hl(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!Ul(e))throw Error(S(40));return e._reactRootContainer?(Ht(function(){Hl(null,null,e,!1,function(){e._reactRootContainer=null,e[tt]=null})}),!0):!1};_e.unstable_batchedUpdates=bi;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ul(n))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return Hl(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function Vc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Vc)}catch(e){console.error(e)}}Vc(),Vs.exports=_e;var jp=Vs.exports,vs=jp;So.createRoot=vs.createRoot,So.hydrateRoot=vs.hydrateRoot;function Ip(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function gs(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),n.push.apply(n,r)}return n}function ys(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?gs(Object(n),!0).forEach(function(r){Ip(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gs(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Rp(e,t){if(e==null)return{};var n={},r=Object.keys(e),l,o;for(o=0;o<r.length;o++)l=r[o],!(t.indexOf(l)>=0)&&(n[l]=e[l]);return n}function Mp(e,t){if(e==null)return{};var n=Rp(e,t),r,l;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(l=0;l<o.length;l++)r=o[l],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Lp(e,t){return zp(e)||Ap(e,t)||Dp(e,t)||Fp()}function zp(e){if(Array.isArray(e))return e}function Ap(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var n=[],r=!0,l=!1,o=void 0;try{for(var i=e[Symbol.iterator](),u;!(r=(u=i.next()).done)&&(n.push(u.value),!(t&&n.length===t));r=!0);}catch(s){l=!0,o=s}finally{try{!r&&i.return!=null&&i.return()}finally{if(l)throw o}}return n}}function Dp(e,t){if(e){if(typeof e=="string")return ws(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ws(e,t)}}function ws(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Fp(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ss(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),n.push.apply(n,r)}return n}function Es(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Ss(Object(n),!0).forEach(function(r){$p(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ss(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Up(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){return t.reduceRight(function(l,o){return o(l)},r)}}function Ln(e){return function t(){for(var n=this,r=arguments.length,l=new Array(r),o=0;o<r;o++)l[o]=arguments[o];return l.length>=e.length?e.apply(this,l):function(){for(var i=arguments.length,u=new Array(i),s=0;s<i;s++)u[s]=arguments[s];return t.apply(n,[].concat(l,u))}}}function kl(e){return{}.toString.call(e).includes("Object")}function Hp(e){return!Object.keys(e).length}function ar(e){return typeof e=="function"}function Bp(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Vp(e,t){return kl(t)||Et("changeType"),Object.keys(t).some(function(n){return!Bp(e,n)})&&Et("changeField"),t}function Kp(e){ar(e)||Et("selectorType")}function Wp(e){ar(e)||kl(e)||Et("handlerType"),kl(e)&&Object.values(e).some(function(t){return!ar(t)})&&Et("handlersType")}function Gp(e){e||Et("initialIsRequired"),kl(e)||Et("initialType"),Hp(e)&&Et("initialContent")}function Qp(e,t){throw new Error(e[t]||e.default)}var Yp={initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"},Et=Ln(Qp)(Yp),$r={changes:Vp,selector:Kp,handler:Wp,initial:Gp};function Xp(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};$r.initial(e),$r.handler(t);var n={current:e},r=Ln(qp)(n,t),l=Ln(Zp)(n),o=Ln($r.changes)(e),i=Ln(Jp)(n);function u(){var c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(v){return v};return $r.selector(c),c(n.current)}function s(c){Up(r,l,o,i)(c)}return[u,s]}function Jp(e,t){return ar(t)?t(e.current):t}function Zp(e,t){return e.current=Es(Es({},e.current),t),t}function qp(e,t,n){return ar(t)?t(e.current):Object.keys(n).forEach(function(r){var l;return(l=t[r])===null||l===void 0?void 0:l.call(t,e.current[r])}),n}var bp={create:Xp},em={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};function tm(e){return function t(){for(var n=this,r=arguments.length,l=new Array(r),o=0;o<r;o++)l[o]=arguments[o];return l.length>=e.length?e.apply(this,l):function(){for(var i=arguments.length,u=new Array(i),s=0;s<i;s++)u[s]=arguments[s];return t.apply(n,[].concat(l,u))}}}function nm(e){return{}.toString.call(e).includes("Object")}function rm(e){return e||xs("configIsRequired"),nm(e)||xs("configType"),e.urls?(lm(),{paths:{vs:e.urls.monacoBase}}):e}function lm(){console.warn(Kc.deprecation)}function om(e,t){throw new Error(e[t]||e.default)}var Kc={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:`Deprecation warning!
    You are using deprecated way of configuration.

    Instead of using
      monaco.config({ urls: { monacoBase: '...' } })
    use
      monaco.config({ paths: { vs: '...' } })

    For more please check the link https://github.com/suren-atoyan/monaco-loader#config
  `},xs=tm(om)(Kc),im={config:rm},um=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(l){return n.reduceRight(function(o,i){return i(o)},l)}};function Wc(e,t){return Object.keys(t).forEach(function(n){t[n]instanceof Object&&e[n]&&Object.assign(t[n],Wc(e[n],t[n]))}),ys(ys({},e),t)}var sm={type:"cancelation",msg:"operation is manually canceled"};function vo(e){var t=!1,n=new Promise(function(r,l){e.then(function(o){return t?l(sm):r(o)}),e.catch(l)});return n.cancel=function(){return t=!0},n}var am=bp.create({config:em,isInitialized:!1,resolve:null,reject:null,monaco:null}),Gc=Lp(am,2),gr=Gc[0],Bl=Gc[1];function cm(e){var t=im.config(e),n=t.monaco,r=Mp(t,["monaco"]);Bl(function(l){return{config:Wc(l.config,r),monaco:n}})}function fm(){var e=gr(function(t){var n=t.monaco,r=t.isInitialized,l=t.resolve;return{monaco:n,isInitialized:r,resolve:l}});if(!e.isInitialized){if(Bl({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),vo(go);if(window.monaco&&window.monaco.editor)return Qc(window.monaco),e.resolve(window.monaco),vo(go);um(dm,mm)(hm)}return vo(go)}function dm(e){return document.body.appendChild(e)}function pm(e){var t=document.createElement("script");return e&&(t.src=e),t}function mm(e){var t=gr(function(r){var l=r.config,o=r.reject;return{config:l,reject:o}}),n=pm("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function hm(){var e=gr(function(n){var r=n.config,l=n.resolve,o=n.reject;return{config:r,resolve:l,reject:o}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(n){Qc(n),e.resolve(n)},function(n){e.reject(n)})}function Qc(e){gr().monaco||Bl({monaco:e})}function vm(){return gr(function(e){var t=e.monaco;return t})}var go=new Promise(function(e,t){return Bl({resolve:e,reject:t})}),Yc={config:cm,init:fm,__getMonacoInstance:vm},gm={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},yo=gm,ym={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},wm=ym;function Sm({children:e}){return Lt.createElement("div",{style:wm.container},e)}var Em=Sm,xm=Em;function km({width:e,height:t,isEditorReady:n,loading:r,_ref:l,className:o,wrapperProps:i}){return Lt.createElement("section",{style:{...yo.wrapper,width:e,height:t},...i},!n&&Lt.createElement(xm,null,r),Lt.createElement("div",{ref:l,style:{...yo.fullWidth,...!n&&yo.hide},className:o}))}var Cm=km,Xc=j.memo(Cm);function _m(e){j.useEffect(e,[])}var Jc=_m;function Nm(e,t,n=!0){let r=j.useRef(!0);j.useEffect(r.current||!n?()=>{r.current=!1}:e,t)}var Ee=Nm;function Kn(){}function ln(e,t,n,r){return Pm(e,r)||Om(e,t,n,r)}function Pm(e,t){return e.editor.getModel(Zc(e,t))}function Om(e,t,n,r){return e.editor.createModel(t,n,r?Zc(e,r):void 0)}function Zc(e,t){return e.Uri.parse(t)}function Tm({original:e,modified:t,language:n,originalLanguage:r,modifiedLanguage:l,originalModelPath:o,modifiedModelPath:i,keepCurrentOriginalModel:u=!1,keepCurrentModifiedModel:s=!1,theme:c="light",loading:v="Loading...",options:h={},height:p="100%",width:m="100%",className:g,wrapperProps:E={},beforeMount:$=Kn,onMount:f=Kn}){let[a,d]=j.useState(!1),[y,x]=j.useState(!0),C=j.useRef(null),k=j.useRef(null),O=j.useRef(null),z=j.useRef(f),P=j.useRef($),b=j.useRef(!1);Jc(()=>{let R=Yc.init();return R.then(U=>(k.current=U)&&x(!1)).catch(U=>(U==null?void 0:U.type)!=="cancelation"&&console.error("Monaco initialization: error:",U)),()=>C.current?ot():R.cancel()}),Ee(()=>{if(C.current&&k.current){let R=C.current.getOriginalEditor(),U=ln(k.current,e||"",r||n||"text",o||"");U!==R.getModel()&&R.setModel(U)}},[o],a),Ee(()=>{if(C.current&&k.current){let R=C.current.getModifiedEditor(),U=ln(k.current,t||"",l||n||"text",i||"");U!==R.getModel()&&R.setModel(U)}},[i],a),Ee(()=>{let R=C.current.getModifiedEditor();R.getOption(k.current.editor.EditorOption.readOnly)?R.setValue(t||""):t!==R.getValue()&&(R.executeEdits("",[{range:R.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),R.pushUndoStop())},[t],a),Ee(()=>{var R,U;(U=(R=C.current)==null?void 0:R.getModel())==null||U.original.setValue(e||"")},[e],a),Ee(()=>{let{original:R,modified:U}=C.current.getModel();k.current.editor.setModelLanguage(R,r||n||"text"),k.current.editor.setModelLanguage(U,l||n||"text")},[n,r,l],a),Ee(()=>{var R;(R=k.current)==null||R.editor.setTheme(c)},[c],a),Ee(()=>{var R;(R=C.current)==null||R.updateOptions(h)},[h],a);let He=j.useCallback(()=>{var Pe;if(!k.current)return;P.current(k.current);let R=ln(k.current,e||"",r||n||"text",o||""),U=ln(k.current,t||"",l||n||"text",i||"");(Pe=C.current)==null||Pe.setModel({original:R,modified:U})},[n,t,l,e,r,o,i]),Be=j.useCallback(()=>{var R;!b.current&&O.current&&(C.current=k.current.editor.createDiffEditor(O.current,{automaticLayout:!0,...h}),He(),(R=k.current)==null||R.editor.setTheme(c),d(!0),b.current=!0)},[h,c,He]);j.useEffect(()=>{a&&z.current(C.current,k.current)},[a]),j.useEffect(()=>{!y&&!a&&Be()},[y,a,Be]);function ot(){var U,Pe,_,I;let R=(U=C.current)==null?void 0:U.getModel();u||((Pe=R==null?void 0:R.original)==null||Pe.dispose()),s||((_=R==null?void 0:R.modified)==null||_.dispose()),(I=C.current)==null||I.dispose()}return Lt.createElement(Xc,{width:m,height:p,isEditorReady:a,loading:v,_ref:O,className:g,wrapperProps:E})}var jm=Tm;j.memo(jm);function Im(e){let t=j.useRef();return j.useEffect(()=>{t.current=e},[e]),t.current}var Rm=Im,Ur=new Map;function Mm({defaultValue:e,defaultLanguage:t,defaultPath:n,value:r,language:l,path:o,theme:i="light",line:u,loading:s="Loading...",options:c={},overrideServices:v={},saveViewState:h=!0,keepCurrentModel:p=!1,width:m="100%",height:g="100%",className:E,wrapperProps:$={},beforeMount:f=Kn,onMount:a=Kn,onChange:d,onValidate:y=Kn}){let[x,C]=j.useState(!1),[k,O]=j.useState(!0),z=j.useRef(null),P=j.useRef(null),b=j.useRef(null),He=j.useRef(a),Be=j.useRef(f),ot=j.useRef(),R=j.useRef(r),U=Rm(o),Pe=j.useRef(!1),_=j.useRef(!1);Jc(()=>{let T=Yc.init();return T.then(M=>(z.current=M)&&O(!1)).catch(M=>(M==null?void 0:M.type)!=="cancelation"&&console.error("Monaco initialization: error:",M)),()=>P.current?L():T.cancel()}),Ee(()=>{var M,ee,we,Ve;let T=ln(z.current,e||r||"",t||l||"",o||n||"");T!==((M=P.current)==null?void 0:M.getModel())&&(h&&Ur.set(U,(ee=P.current)==null?void 0:ee.saveViewState()),(we=P.current)==null||we.setModel(T),h&&((Ve=P.current)==null||Ve.restoreViewState(Ur.get(o))))},[o],x),Ee(()=>{var T;(T=P.current)==null||T.updateOptions(c)},[c],x),Ee(()=>{!P.current||r===void 0||(P.current.getOption(z.current.editor.EditorOption.readOnly)?P.current.setValue(r):r!==P.current.getValue()&&(_.current=!0,P.current.executeEdits("",[{range:P.current.getModel().getFullModelRange(),text:r,forceMoveMarkers:!0}]),P.current.pushUndoStop(),_.current=!1))},[r],x),Ee(()=>{var M,ee;let T=(M=P.current)==null?void 0:M.getModel();T&&l&&((ee=z.current)==null||ee.editor.setModelLanguage(T,l))},[l],x),Ee(()=>{var T;u!==void 0&&((T=P.current)==null||T.revealLine(u))},[u],x),Ee(()=>{var T;(T=z.current)==null||T.editor.setTheme(i)},[i],x);let I=j.useCallback(()=>{var T;if(!(!b.current||!z.current)&&!Pe.current){Be.current(z.current);let M=o||n,ee=ln(z.current,r||e||"",t||l||"",M||"");P.current=(T=z.current)==null?void 0:T.editor.create(b.current,{model:ee,automaticLayout:!0,...c},v),h&&P.current.restoreViewState(Ur.get(M)),z.current.editor.setTheme(i),u!==void 0&&P.current.revealLine(u),C(!0),Pe.current=!0}},[e,t,n,r,l,o,c,v,h,i,u]);j.useEffect(()=>{x&&He.current(P.current,z.current)},[x]),j.useEffect(()=>{!k&&!x&&I()},[k,x,I]),R.current=r,j.useEffect(()=>{var T,M;x&&d&&((T=ot.current)==null||T.dispose(),ot.current=(M=P.current)==null?void 0:M.onDidChangeModelContent(ee=>{_.current||d(P.current.getValue(),ee)}))},[x,d]),j.useEffect(()=>{if(x){let T=z.current.editor.onDidChangeMarkers(M=>{var we;let ee=(we=P.current.getModel())==null?void 0:we.uri;if(ee&&M.find(Ve=>Ve.path===ee.path)){let Ve=z.current.editor.getModelMarkers({resource:ee});y==null||y(Ve)}});return()=>{T==null||T.dispose()}}return()=>{}},[x,y]);function L(){var T,M;(T=ot.current)==null||T.dispose(),p?h&&Ur.set(o,P.current.saveViewState()):(M=P.current.getModel())==null||M.dispose(),P.current.dispose()}return Lt.createElement(Xc,{width:m,height:g,isEditorReady:x,loading:s,_ref:b,className:E,wrapperProps:$})}var Lm=Mm,zm=j.memo(Lm),ks=zm;const Am=({originalPrompt:e,enhancedPrompt:t,isLoading:n,onPromptChange:r,onEnhance:l})=>{const o=async i=>{try{await navigator.clipboard.writeText(i)}catch(u){console.error("Failed to copy to clipboard:",u)}};return w.jsxs("div",{className:"h-full flex flex-col space-y-4",children:[w.jsxs("div",{className:"flex-1 bg-white rounded-lg shadow-sm border border-gray-200",children:[w.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200",children:[w.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Original Prompt"}),w.jsx("button",{onClick:l,disabled:n||!e.trim(),className:`px-4 py-2 rounded-md text-sm font-medium ${n||!e.trim()?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"}`,children:n?w.jsxs("div",{className:"flex items-center space-x-2",children:[w.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),w.jsx("span",{children:"Enhancing..."})]}):"Enhance Prompt"})]}),w.jsx("div",{className:"h-64",children:w.jsx(ks,{height:"100%",defaultLanguage:"plaintext",value:e,onChange:i=>r(i||""),options:{minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,lineNumbers:"off",wordWrap:"on",padding:{top:16,bottom:16},theme:"vs"}})})]}),w.jsxs("div",{className:"flex-1 bg-white rounded-lg shadow-sm border border-gray-200",children:[w.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-200",children:[w.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Enhanced Prompt"}),t&&w.jsxs("div",{className:"flex space-x-2",children:[w.jsx("button",{onClick:()=>o(t),className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded hover:bg-gray-50",children:"Copy"}),w.jsx("button",{onClick:()=>r(t),className:"px-3 py-1 text-sm text-primary-600 hover:text-primary-700 border border-primary-300 rounded hover:bg-primary-50",children:"Use as Input"})]})]}),w.jsx("div",{className:"h-64",children:t?w.jsx(ks,{height:"100%",defaultLanguage:"plaintext",value:t,options:{readOnly:!0,minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,lineNumbers:"off",wordWrap:"on",padding:{top:16,bottom:16},theme:"vs"}}):w.jsx("div",{className:"h-full flex items-center justify-center text-gray-500",children:w.jsxs("div",{className:"text-center",children:[w.jsx("p",{className:"text-lg mb-2",children:"Enhanced prompt will appear here"}),w.jsx("p",{className:"text-sm",children:'Enter a prompt above and click "Enhance Prompt" to get started'})]})})})]}),w.jsxs("div",{className:"flex justify-between items-center bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3",children:[w.jsxs("div",{className:"flex space-x-4 text-sm text-gray-600",children:[w.jsxs("span",{children:["Characters: ",e.length]}),w.jsxs("span",{children:["Words:"," ",e.split(/\s+/).filter(i=>i.length>0).length]})]}),w.jsx("div",{className:"flex space-x-2",children:w.jsx("button",{onClick:()=>r(""),className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded hover:bg-gray-50",children:"Clear"})})]})]})},Dm=({onSubmit:e,onClose:t})=>{const[n,r]=j.useState(""),[l,o]=j.useState(""),i=u=>{if(u.preventDefault(),!n.trim()){o("Please enter your Gemini API key");return}if(!n.startsWith("AIza")){o('Invalid API key format. Gemini API keys start with "AIza"');return}e(n.trim())};return w.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:w.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[w.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:w.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Configure Gemini API Key"})}),w.jsxs("form",{onSubmit:i,className:"px-6 py-4",children:[w.jsxs("div",{className:"mb-4",children:[w.jsx("label",{htmlFor:"apiKey",className:"block text-sm font-medium text-gray-700 mb-2",children:"Google Gemini API Key"}),w.jsx("input",{type:"password",id:"apiKey",value:n,onChange:u=>{r(u.target.value),o("")},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"Enter your Gemini API key",autoFocus:!0}),l&&w.jsx("p",{className:"mt-2 text-sm text-red-600",children:l})]}),w.jsxs("div",{className:"mb-4 p-3 bg-blue-50 rounded-md",children:[w.jsx("p",{className:"text-sm text-blue-800",children:w.jsx("strong",{children:"How to get your API key:"})}),w.jsxs("ol",{className:"text-sm text-blue-700 mt-2 list-decimal list-inside space-y-1",children:[w.jsxs("li",{children:["Visit"," ",w.jsx("a",{href:"https://makersuite.google.com/app/apikey",className:"underline",target:"_blank",rel:"noopener noreferrer",children:"Google AI Studio"})]}),w.jsx("li",{children:"Sign in with your Google account"}),w.jsx("li",{children:'Click "Create API Key"'}),w.jsx("li",{children:"Copy the generated key and paste it here"})]})]}),w.jsxs("div",{className:"flex justify-end space-x-3",children:[w.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Cancel"}),w.jsx("button",{type:"submit",className:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:"Save API Key"})]})]})]})})};/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var Cs;(function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT"})(Cs||(Cs={}));var _s;(function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"})(_s||(_s={}));var Ns;(function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"})(Ns||(Ns={}));var Ps;(function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER"})(Ps||(Ps={}));var Cl;(function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.OTHER="OTHER"})(Cl||(Cl={}));var Os;(function(e){e.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",e.RETRIEVAL_QUERY="RETRIEVAL_QUERY",e.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",e.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",e.CLASSIFICATION="CLASSIFICATION",e.CLUSTERING="CLUSTERING"})(Os||(Os={}));/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class cr extends Error{constructor(t){super(`[GoogleGenerativeAI Error]: ${t}`)}}class Ts extends cr{constructor(t,n){super(t),this.response=n}}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Fm="https://generativelanguage.googleapis.com",$m="v1",Um="0.2.1",Hm="genai-js";var Bt;(function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.EMBED_CONTENT="embedContent",e.BATCH_EMBED_CONTENTS="batchEmbedContents"})(Bt||(Bt={}));class yr{constructor(t,n,r,l){this.model=t,this.task=n,this.apiKey=r,this.stream=l}toString(){let t=`${Fm}/${$m}/${this.model}:${this.task}`;return this.stream&&(t+="?alt=sse"),t}}function Bm(){return`${Hm}/${Um}`}async function wr(e,t,n){let r;try{if(r=await fetch(e.toString(),Object.assign(Object.assign({},Vm(n)),{method:"POST",headers:{"Content-Type":"application/json","x-goog-api-client":Bm(),"x-goog-api-key":e.apiKey},body:t})),!r.ok){let l="";try{const o=await r.json();l=o.error.message,o.error.details&&(l+=` ${JSON.stringify(o.error.details)}`)}catch{}throw new Error(`[${r.status} ${r.statusText}] ${l}`)}}catch(l){const o=new cr(`Error fetching from ${e.toString()}: ${l.message}`);throw o.stack=l.stack,o}return r}function Vm(e){const t={};if((e==null?void 0:e.timeout)>=0){const n=new AbortController,r=n.signal;setTimeout(()=>n.abort(),e.timeout),t.signal=r}return t}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function uu(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),qc(e.candidates[0]))throw new Ts(`${_l(e)}`,e);return Km(e)}else if(e.promptFeedback)throw new Ts(`Text not available. ${_l(e)}`,e);return""},e}function Km(e){var t,n,r,l;return!((l=(r=(n=(t=e.candidates)===null||t===void 0?void 0:t[0].content)===null||n===void 0?void 0:n.parts)===null||r===void 0?void 0:r[0])===null||l===void 0)&&l.text?e.candidates[0].content.parts[0].text:""}const Wm=[Cl.RECITATION,Cl.SAFETY];function qc(e){return!!e.finishReason&&Wm.includes(e.finishReason)}function _l(e){var t,n,r;let l="";if((!e.candidates||e.candidates.length===0)&&e.promptFeedback)l+="Response was blocked",!((t=e.promptFeedback)===null||t===void 0)&&t.blockReason&&(l+=` due to ${e.promptFeedback.blockReason}`),!((n=e.promptFeedback)===null||n===void 0)&&n.blockReasonMessage&&(l+=`: ${e.promptFeedback.blockReasonMessage}`);else if(!((r=e.candidates)===null||r===void 0)&&r[0]){const o=e.candidates[0];qc(o)&&(l+=`Candidate was blocked due to ${o.finishReason}`,o.finishMessage&&(l+=`: ${o.finishMessage}`))}return l}function fr(e){return this instanceof fr?(this.v=e,this):new fr(e)}function Gm(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),l,o=[];return l={},i("next"),i("throw"),i("return"),l[Symbol.asyncIterator]=function(){return this},l;function i(p){r[p]&&(l[p]=function(m){return new Promise(function(g,E){o.push([p,m,g,E])>1||u(p,m)})})}function u(p,m){try{s(r[p](m))}catch(g){h(o[0][3],g)}}function s(p){p.value instanceof fr?Promise.resolve(p.value.v).then(c,v):h(o[0][2],p)}function c(p){u("next",p)}function v(p){u("throw",p)}function h(p,m){p(m),o.shift(),o.length&&u(o[0][0],o[0][1])}}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const js=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function Qm(e){const t=e.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0})),n=Jm(t),[r,l]=n.tee();return{stream:Xm(r),response:Ym(l)}}async function Ym(e){const t=[],n=e.getReader();for(;;){const{done:r,value:l}=await n.read();if(r)return uu(Zm(t));t.push(l)}}function Xm(e){return Gm(this,arguments,function*(){const n=e.getReader();for(;;){const{value:r,done:l}=yield fr(n.read());if(l)break;yield yield fr(uu(r))}})}function Jm(e){const t=e.getReader();return new ReadableStream({start(r){let l="";return o();function o(){return t.read().then(({value:i,done:u})=>{if(u){if(l.trim()){r.error(new cr("Failed to parse stream"));return}r.close();return}l+=i;let s=l.match(js),c;for(;s;){try{c=JSON.parse(s[1])}catch{r.error(new cr(`Error parsing JSON response: "${s[1]}"`));return}r.enqueue(c),l=l.substring(s[0].length),s=l.match(js)}return o()})}}})}function Zm(e){const t=e[e.length-1],n={promptFeedback:t==null?void 0:t.promptFeedback};for(const r of e)if(r.candidates)for(const l of r.candidates){const o=l.index;if(n.candidates||(n.candidates=[]),n.candidates[o]||(n.candidates[o]={index:l.index}),n.candidates[o].citationMetadata=l.citationMetadata,n.candidates[o].finishReason=l.finishReason,n.candidates[o].finishMessage=l.finishMessage,n.candidates[o].safetyRatings=l.safetyRatings,l.content&&l.content.parts){n.candidates[o].content||(n.candidates[o].content={role:l.content.role||"user",parts:[{text:""}]});for(const i of l.content.parts)i.text&&(n.candidates[o].content.parts[0].text+=i.text)}}return n}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function bc(e,t,n,r){const l=new yr(t,Bt.STREAM_GENERATE_CONTENT,e,!0),o=await wr(l,JSON.stringify(n),r);return Qm(o)}async function ef(e,t,n,r){const l=new yr(t,Bt.GENERATE_CONTENT,e,!1),i=await(await wr(l,JSON.stringify(n),r)).json();return{response:uu(i)}}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function Wn(e,t){let n=[];if(typeof e=="string")n=[{text:e}];else for(const r of e)typeof r=="string"?n.push({text:r}):n.push(r);return{role:t,parts:n}}function wo(e){return e.contents?e:{contents:[Wn(e,"user")]}}function qm(e){return typeof e=="string"||Array.isArray(e)?{content:Wn(e,"user")}:e}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Is="SILENT_ERROR";class bm{constructor(t,n,r,l){this.model=n,this.params=r,this.requestOptions=l,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=t,r!=null&&r.history&&(this._history=r.history.map(o=>{if(!o.role)throw new Error("Missing role for history item: "+JSON.stringify(o));return Wn(o.parts,o.role)}))}async getHistory(){return await this._sendPromise,this._history}async sendMessage(t){var n,r;await this._sendPromise;const l=Wn(t,"user"),o={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(r=this.params)===null||r===void 0?void 0:r.generationConfig,contents:[...this._history,l]};let i;return this._sendPromise=this._sendPromise.then(()=>ef(this._apiKey,this.model,o,this.requestOptions)).then(u=>{var s;if(u.response.candidates&&u.response.candidates.length>0){this._history.push(l);const c=Object.assign({parts:[],role:"model"},(s=u.response.candidates)===null||s===void 0?void 0:s[0].content);this._history.push(c)}else{const c=_l(u.response);c&&console.warn(`sendMessage() was unsuccessful. ${c}. Inspect response object for details.`)}i=u}),await this._sendPromise,i}async sendMessageStream(t){var n,r;await this._sendPromise;const l=Wn(t,"user"),o={safetySettings:(n=this.params)===null||n===void 0?void 0:n.safetySettings,generationConfig:(r=this.params)===null||r===void 0?void 0:r.generationConfig,contents:[...this._history,l]},i=bc(this._apiKey,this.model,o,this.requestOptions);return this._sendPromise=this._sendPromise.then(()=>i).catch(u=>{throw new Error(Is)}).then(u=>u.response).then(u=>{if(u.candidates&&u.candidates.length>0){this._history.push(l);const s=Object.assign({},u.candidates[0].content);s.role||(s.role="model"),this._history.push(s)}else{const s=_l(u);s&&console.warn(`sendMessageStream() was unsuccessful. ${s}. Inspect response object for details.`)}}).catch(u=>{u.message!==Is&&console.error(u)}),i}}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function eh(e,t,n,r){const l=new yr(t,Bt.COUNT_TOKENS,e,!1);return(await wr(l,JSON.stringify(Object.assign(Object.assign({},n),{model:t})),r)).json()}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function th(e,t,n,r){const l=new yr(t,Bt.EMBED_CONTENT,e,!1);return(await wr(l,JSON.stringify(n),r)).json()}async function nh(e,t,n,r){const l=new yr(t,Bt.BATCH_EMBED_CONTENTS,e,!1),o=n.requests.map(u=>Object.assign(Object.assign({},u),{model:t}));return(await wr(l,JSON.stringify({requests:o}),r)).json()}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class rh{constructor(t,n,r){this.apiKey=t,n.model.includes("/")?this.model=n.model:this.model=`models/${n.model}`,this.generationConfig=n.generationConfig||{},this.safetySettings=n.safetySettings||[],this.requestOptions=r||{}}async generateContent(t){const n=wo(t);return ef(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings},n),this.requestOptions)}async generateContentStream(t){const n=wo(t);return bc(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings},n),this.requestOptions)}startChat(t){return new bm(this.apiKey,this.model,t,this.requestOptions)}async countTokens(t){const n=wo(t);return eh(this.apiKey,this.model,n)}async embedContent(t){const n=qm(t);return th(this.apiKey,this.model,n)}async batchEmbedContents(t){return nh(this.apiKey,this.model,t,this.requestOptions)}}/**
 * @license
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class lh{constructor(t){this.apiKey=t}getGenerativeModel(t,n){if(!t.model)throw new cr("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new rh(this.apiKey,t,n)}}class oh{constructor(){Er(this,"genAI",null);Er(this,"model",null);Er(this,"apiKey",null)}initialize(t){this.apiKey=t.apiKey,this.genAI=new lh(t.apiKey);const n=t.model||"gemini-1.5-flash";this.model=this.genAI.getGenerativeModel({model:n,generationConfig:{temperature:t.temperature||.3,maxOutputTokens:t.maxTokens||2048}})}async listModels(){if(!this.apiKey)throw new Error("API key not set. Please initialize the service first.");try{const t=await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${this.apiKey}`);if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const n=await t.json();return{models:n.models.filter(l=>l.supportedGenerationMethods.includes("generateContent")),nextPageToken:n.nextPageToken}}catch(t){throw console.error("Error fetching models:",t),new Error(`Failed to fetch models: ${t instanceof Error?t.message:"Unknown error"}`)}}async enhancePrompt(t){if(!this.model)throw new Error("Gemini service not initialized. Please set your API key.");const n=this.buildSystemPrompt(t.mode,t.style),r=this.buildUserPrompt(t);try{const i=(await(await this.model.generateContent([{text:n},{text:r}])).response).text();return{enhancedPrompt:i.trim(),suggestions:this.extractSuggestions(i),improvements:this.extractImprovements(i)}}catch(l){throw console.error("Error calling Gemini API:",l),new Error(`Failed to enhance prompt: ${l instanceof Error?l.message:"Unknown error"}`)}}buildSystemPrompt(t,n){const r="You are an expert prompt engineer. Your task is to enhance and improve prompts to make them more effective, clear, and likely to produce better results from AI systems.",l={quick:"Focus on quick improvements: clarity, specificity, and structure. Make the prompt more actionable and precise.",structured:"Break down the prompt into clear sections: Role, Context, Instructions, Goal, and Constraints. Ensure each section is well-defined.",template:"Create a reusable template structure that can be adapted for similar use cases. Include placeholders and clear sections.",batch:"Process this as part of a batch operation. Focus on consistency and standardization across multiple prompts."},o=n?{creative:"Emphasize creativity, exploration, and open-ended thinking. Encourage innovative approaches.",concise:"Keep the enhanced prompt concise and to-the-point while maintaining effectiveness.",technical:"Focus on technical accuracy, precision, and detailed specifications. Use appropriate technical language.",detailed:"Provide comprehensive, detailed instructions with examples and clear expectations."}[n]:"";return`${r}

${l[t]}

${o}

Return only the enhanced prompt without additional commentary.`}buildUserPrompt(t){let n=`Please enhance this prompt:

"${t.originalPrompt}"`;return t.context&&(n+=`

Additional context: ${t.context}`),n}extractSuggestions(t){const n=[];return t.includes("consider")&&n.push("Consider adding more specific examples"),t.includes("format")&&n.push("Specify the desired output format"),t.includes("context")&&n.push("Provide additional context for better results"),n}extractImprovements(t){const n=[];return t.length>100&&n.push("Added more detailed instructions"),t.includes(":")&&n.push("Improved structure with clear sections"),t.includes("example")&&n.push("Included examples for clarity"),n}updateModel(t){if(!this.genAI)throw new Error("Service not initialized. Please set API key first.");this.model=this.genAI.getGenerativeModel({model:t,generationConfig:{temperature:.3,maxOutputTokens:2048}})}isInitialized(){return this.model!==null}}const jt=new oh,ih=({selectedModel:e,onModelSelect:t,disabled:n=!1})=>{const[r,l]=j.useState([]),[o,i]=j.useState(!1),[u,s]=j.useState(null);j.useEffect(()=>{c()},[]);const c=async()=>{if(jt.isInitialized()){i(!0),s(null);try{const m=await jt.listModels();l(m.models),!e&&m.models.length>0&&t(m.models[0].baseModelId)}catch(m){console.error("Failed to load models:",m),s(m instanceof Error?m.message:"Failed to load models")}finally{i(!1)}}},v=m=>{const g=m.target.value;t(g)},h=m=>m.displayName||m.baseModelId,p=m=>{var E;const g=`${((E=m.inputTokenLimit)==null?void 0:E.toLocaleString())||"N/A"} input tokens`;return m.description?`${m.description} (${g})`:g};return u?w.jsxs("div",{className:"mb-4",children:[w.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Selection"}),w.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:w.jsxs("div",{className:"flex items-center",children:[w.jsxs("div",{className:"text-red-600 text-sm",children:[w.jsx("strong",{children:"Error loading models:"})," ",u]}),w.jsx("button",{onClick:c,className:"ml-auto text-red-600 hover:text-red-800 text-sm underline",children:"Retry"})]})})]}):w.jsxs("div",{className:"mb-4",children:[w.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Selection"}),w.jsxs("div",{className:"relative",children:[w.jsx("select",{value:e||"",onChange:v,disabled:n||o||r.length===0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed",children:o?w.jsx("option",{value:"",children:"Loading models..."}):r.length===0?w.jsx("option",{value:"",children:"No models available"}):w.jsxs(w.Fragment,{children:[w.jsx("option",{value:"",children:"Select a model"}),r.map(m=>w.jsx("option",{value:m.baseModelId,children:h(m)},m.baseModelId))]})}),o&&w.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:w.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"})})]}),e&&r.length>0&&w.jsx("div",{className:"mt-2 text-sm text-gray-600",children:(()=>{const m=r.find(g=>g.baseModelId===e);return m?w.jsxs("div",{children:[w.jsx("div",{className:"font-medium",children:h(m)}),w.jsx("div",{className:"text-xs text-gray-500 mt-1",children:p(m)})]}):null})()})]})};function uh(){const[e,t]=j.useState({originalPrompt:"",enhancedPrompt:"",isLoading:!1,error:null,apiKey:null,currentMode:"quick",currentStyle:"detailed"}),[n,r]=j.useState(!1),[l,o]=j.useState(null);j.useEffect(()=>{window.electronAPI&&Promise.all([window.electronAPI.getApiKey(),window.electronAPI.getSelectedModel()]).then(([{apiKey:m},{modelId:g}])=>{m?(t(E=>({...E,apiKey:m})),jt.initialize({apiKey:m,model:g||void 0}),o(g)):r(!0)})},[]);const i=async m=>{try{jt.initialize({apiKey:m,model:l||void 0}),window.electronAPI&&await window.electronAPI.storeApiKey(m),t(g=>({...g,apiKey:m})),r(!1)}catch{t(E=>({...E,error:"Failed to initialize Gemini service. Please check your API key."}))}},u=async m=>{try{jt.updateModel(m),window.electronAPI&&await window.electronAPI.storeSelectedModel(m),o(m)}catch{t(E=>({...E,error:"Failed to update model selection."}))}},s=async()=>{if(!e.originalPrompt.trim()){t(m=>({...m,error:"Please enter a prompt to enhance."}));return}if(!jt.isInitialized()){r(!0);return}t(m=>({...m,isLoading:!0,error:null}));try{const m=await jt.enhancePrompt({originalPrompt:e.originalPrompt,mode:e.currentMode,style:e.currentStyle});t(g=>({...g,enhancedPrompt:m.enhancedPrompt,isLoading:!1}))}catch(m){t(g=>({...g,error:m instanceof Error?m.message:"An error occurred while enhancing the prompt.",isLoading:!1}))}},c=m=>{t(g=>({...g,currentMode:m}))},v=m=>{t(g=>({...g,currentStyle:m}))},h=m=>{t(g=>({...g,originalPrompt:m}))},p=()=>{t(m=>({...m,error:null}))};return w.jsxs("div",{className:"h-screen bg-gray-50 flex flex-col",children:[w.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200 px-6 py-4",children:w.jsxs("div",{className:"flex items-center justify-between",children:[w.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Prompt Enhancer"}),w.jsxs("div",{className:"flex items-center space-x-4",children:[w.jsxs("select",{value:e.currentMode,onChange:m=>c(m.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[w.jsx("option",{value:"quick",children:"Quick Enhancement"}),w.jsx("option",{value:"structured",children:"Structured Mode"}),w.jsx("option",{value:"template",children:"Template Mode"})]}),w.jsxs("select",{value:e.currentStyle,onChange:m=>v(m.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[w.jsx("option",{value:"detailed",children:"Detailed"}),w.jsx("option",{value:"concise",children:"Concise"}),w.jsx("option",{value:"creative",children:"Creative"}),w.jsx("option",{value:"technical",children:"Technical"})]}),w.jsx("button",{onClick:()=>r(!0),className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50",children:e.apiKey?"Update API Key":"Set API Key"})]})]})}),e.error&&w.jsx("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4 rounded",children:w.jsxs("div",{className:"flex items-center justify-between",children:[w.jsx("p",{className:"text-red-700",children:e.error}),w.jsx("button",{onClick:p,className:"text-red-400 hover:text-red-600",children:"×"})]})}),e.apiKey&&w.jsx("div",{className:"px-6 mt-4",children:w.jsx(ih,{selectedModel:l,onModelSelect:u,disabled:e.isLoading})}),w.jsx("main",{className:"flex-1 p-6",children:w.jsx(Am,{originalPrompt:e.originalPrompt,enhancedPrompt:e.enhancedPrompt,isLoading:e.isLoading,onPromptChange:h,onEnhance:s})}),n&&w.jsx(Dm,{onSubmit:i,onClose:()=>r(!1)})]})}So.createRoot(document.getElementById("root")).render(w.jsx(Lt.StrictMode,{children:w.jsx(uh,{})}));
