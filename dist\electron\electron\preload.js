"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Define the API that will be exposed to the renderer process
const electronAPI = {
    // API key management
    storeApiKey: (apiKey) => electron_1.ipcRenderer.invoke("store-api-key", apiKey),
    getApiKey: () => electron_1.ipcRenderer.invoke("get-api-key"),
    // Model selection management
    storeSelectedModel: (modelId) => electron_1.ipcRenderer.invoke("store-selected-model", modelId),
    getSelectedModel: () => electron_1.ipcRenderer.invoke("get-selected-model"),
    // File operations
    showSaveDialog: () => electron_1.ipcRenderer.invoke("show-save-dialog"),
    showOpenDialog: () => electron_1.ipcRenderer.invoke("show-open-dialog"),
    // Platform info
    platform: process.platform,
    isDev: () => electron_1.ipcRenderer.invoke("is-dev"),
};
// Expose the API to the renderer process
electron_1.contextBridge.exposeInMainWorld("electronAPI", electronAPI);
