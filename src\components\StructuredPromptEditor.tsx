import { FC, useState, useEffect } from "react";
import MonacoE<PERSON>or from "@monaco-editor/react";
import { StructuredPromptSections, SectionConfig } from "../types";

interface StructuredPromptEditorProps {
  sections: StructuredPromptSections;
  onSectionsChange: (sections: StructuredPromptSections) => void;
  customSections: SectionConfig[];
  onCustomSectionsChange: (sections: SectionConfig[]) => void;
  isLoading: boolean;
  onEnhance: () => void;
}

// Default sections configuration
const DEFAULT_SECTIONS: SectionConfig[] = [
  {
    key: "role",
    label: "Role",
    placeholder: "Define the AI's role and expertise...",
    isRequired: false,
    isCustom: false,
    canDelete: true,
    canRename: true,
  },
  {
    key: "context",
    label: "Context",
    placeholder: "Provide relevant background information...",
    isRequired: false,
    isCustom: false,
    canDelete: true,
    canRename: true,
  },
  {
    key: "instructions",
    label: "Instructions",
    placeholder: "Clear, step-by-step directions...",
    isRequired: true,
    isCustom: false,
    canDelete: false,
    canRename: false,
  },
  {
    key: "goal",
    label: "Goal",
    placeholder: "Specific desired outcome...",
    isRequired: false,
    isCustom: false,
    canDelete: true,
    canRename: true,
  },
  {
    key: "constraints",
    label: "Constraints",
    placeholder: "Limitations, requirements, or guidelines...",
    isRequired: false,
    isCustom: false,
    canDelete: true,
    canRename: true,
  },
  {
    key: "examples",
    label: "Examples",
    placeholder: "Sample inputs/outputs...",
    isRequired: false,
    isCustom: false,
    canDelete: true,
    canRename: true,
  },
  {
    key: "outputFormat",
    label: "Output Format",
    placeholder: "Specify the desired response format...",
    isRequired: false,
    isCustom: false,
    canDelete: true,
    canRename: true,
  },
];

export const StructuredPromptEditor: FC<StructuredPromptEditorProps> = ({
  sections,
  onSectionsChange,
  customSections,
  onCustomSectionsChange,
  isLoading,
  onEnhance,
}) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [newSectionName, setNewSectionName] = useState("");
  const [showAddSection, setShowAddSection] = useState(false);

  // Initialize custom sections if empty
  useEffect(() => {
    if (customSections.length === 0) {
      onCustomSectionsChange(DEFAULT_SECTIONS);
    }
  }, [customSections.length, onCustomSectionsChange]);

  const allSections =
    customSections.length > 0 ? customSections : DEFAULT_SECTIONS;

  const handleSectionChange = (sectionKey: string, value: string) => {
    onSectionsChange({
      ...sections,
      [sectionKey]: value,
    });
  };

  const generateCombinedPrompt = (): string => {
    const parts: string[] = [];

    allSections.forEach((section) => {
      const content = sections[section.key];
      if (content && content.trim()) {
        // Remove "(Optional)" text from the label for the combined output
        const cleanLabel = section.label.replace(/\s*\(Optional\)/i, "");
        parts.push(`**${cleanLabel.toUpperCase()}:**\n${content}`);
      }
    });

    return parts.join("\n\n");
  };

  const handleAddSection = () => {
    if (newSectionName.trim()) {
      const newKey = newSectionName.toLowerCase().replace(/\s+/g, "_");
      const newSection: SectionConfig = {
        key: newKey,
        label: newSectionName.trim(),
        placeholder: `Enter ${newSectionName.toLowerCase()}...`,
        isRequired: false,
        isCustom: true,
        canDelete: true,
        canRename: true,
      };

      onCustomSectionsChange([...allSections, newSection]);
      onSectionsChange({ ...sections, [newKey]: "" });
      setNewSectionName("");
      setShowAddSection(false);
    }
  };

  const handleDeleteSection = (sectionKey: string) => {
    const updatedSections = allSections.filter((s) => s.key !== sectionKey);
    onCustomSectionsChange(updatedSections);

    const updatedContent = { ...sections };
    delete updatedContent[sectionKey];
    onSectionsChange(updatedContent);
  };

  const handleRenameSection = (sectionKey: string, newLabel: string) => {
    const updatedSections = allSections.map((s) =>
      s.key === sectionKey ? { ...s, label: newLabel } : s
    );
    onCustomSectionsChange(updatedSections);
    setEditingSection(null);
  };

  const hasContent = Object.values(sections).some(
    (value) => value && value.trim().length > 0
  );

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">
            Structured Prompt Builder
          </h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowAddSection(!showAddSection)}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded hover:bg-gray-50"
            >
              + Add Section
            </button>
            <button
              onClick={onEnhance}
              disabled={isLoading || !hasContent}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                isLoading || !hasContent
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Enhancing...</span>
                </div>
              ) : (
                "Enhance Structured Prompt"
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Add Section Form */}
      {showAddSection && (
        <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={newSectionName}
              onChange={(e) => setNewSectionName(e.target.value)}
              placeholder="Enter section name..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              onKeyPress={(e) => e.key === "Enter" && handleAddSection()}
            />
            <button
              onClick={handleAddSection}
              disabled={!newSectionName.trim()}
              className="px-3 py-2 bg-primary-600 text-white rounded-md text-sm hover:bg-primary-700 disabled:bg-gray-300"
            >
              Add
            </button>
            <button
              onClick={() => setShowAddSection(false)}
              className="px-3 py-2 text-gray-600 hover:text-gray-900 text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Sections Grid */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-4">
        {allSections.map((section) => (
          <div
            key={section.key}
            className="bg-white rounded-lg shadow-sm border border-gray-200"
          >
            <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
              {editingSection === section.key ? (
                <div className="flex items-center space-x-2 flex-1">
                  <input
                    type="text"
                    defaultValue={section.label}
                    className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                    onBlur={(e) =>
                      handleRenameSection(section.key, e.target.value)
                    }
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        handleRenameSection(section.key, e.currentTarget.value);
                      }
                    }}
                    autoFocus
                  />
                  <button
                    onClick={() => setEditingSection(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✓
                  </button>
                </div>
              ) : (
                <>
                  <h3 className="text-sm font-semibold text-gray-900 flex items-center">
                    {section.label}
                    {section.isRequired && (
                      <span className="text-red-500 ml-1">*</span>
                    )}
                  </h3>
                  <div className="flex items-center space-x-1">
                    {section.canRename && (
                      <button
                        onClick={() => setEditingSection(section.key)}
                        className="text-gray-400 hover:text-gray-600 p-1"
                        title="Rename section"
                      >
                        ✏️
                      </button>
                    )}
                    {section.canDelete && (
                      <button
                        onClick={() => handleDeleteSection(section.key)}
                        className="text-red-400 hover:text-red-600 p-1"
                        title="Delete section"
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </>
              )}
            </div>
            <div className="h-32">
              <MonacoEditor
                height="100%"
                defaultLanguage="plaintext"
                value={sections[section.key] || ""}
                onChange={(value) =>
                  handleSectionChange(section.key, value || "")
                }
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 12,
                  lineNumbers: "off",
                  wordWrap: "on",
                  padding: { top: 8, bottom: 8 },
                  theme: "vs",
                  placeholder: section.placeholder,
                }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Preview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900">
            Combined Preview
          </h3>
        </div>
        <div className="h-40">
          <MonacoEditor
            height="100%"
            defaultLanguage="markdown"
            value={generateCombinedPrompt()}
            options={{
              readOnly: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              fontSize: 12,
              lineNumbers: "off",
              wordWrap: "on",
              padding: { top: 8, bottom: 8 },
              theme: "vs",
            }}
          />
        </div>
      </div>
    </div>
  );
};
