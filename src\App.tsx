import { useState, useEffect } from "react";
import { Editor } from "./components/Editor";
import { StructuredPromptEditor } from "./components/StructuredPromptEditor";
import { TemplateEditor } from "./components/TemplateEditor";
import { ApiKeyModal } from "./components/ApiKeyModal";
import { ModelSelector } from "./components/ModelSelector";
import { geminiService } from "./services/geminiService";
import {
  AppState,
  EnhancementMode,
  EnhancementStyle,
  StructuredPromptSections,
  PromptTemplate,
  SectionConfig,
} from "./types";

function App() {
  const [state, setState] = useState<AppState>({
    originalPrompt: "",
    enhancedPrompt: "",
    isLoading: false,
    error: null,
    apiKey: null,
    currentMode: "quick",
    currentStyle: "detailed",
    structuredSections: {
      role: "",
      context: "",
      instructions: "",
      goal: "",
      constraints: "",
      examples: "",
      outputFormat: "",
    },
    selectedTemplate: null,
    customTemplates: [],
    customSections: [], // Will be populated with default sections
  });

  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  useEffect(() => {
    // Check if API key and selected model are stored
    if (window.electronAPI) {
      Promise.all([
        window.electronAPI.getApiKey(),
        window.electronAPI.getSelectedModel(),
      ]).then(([{ apiKey }, { modelId }]) => {
        if (apiKey) {
          setState((prev) => ({ ...prev, apiKey }));
          geminiService.initialize({
            apiKey,
            model: modelId || undefined,
          });
          setSelectedModel(modelId);
        } else {
          // Only show modal in production if API key is missing
          setShowApiKeyModal(true);
        }
      });
    }
  }, []);

  const handleApiKeySubmit = async (apiKey: string) => {
    try {
      geminiService.initialize({
        apiKey,
        model: selectedModel || undefined,
      });

      if (window.electronAPI) {
        await window.electronAPI.storeApiKey(apiKey);
      }

      setState((prev) => ({ ...prev, apiKey }));
      setShowApiKeyModal(false);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error:
          "Failed to initialize Gemini service. Please check your API key.",
      }));
    }
  };

  const handleModelSelect = async (modelId: string) => {
    try {
      // Update the service to use the new model
      geminiService.updateModel(modelId);

      // Store the selected model
      if (window.electronAPI) {
        await window.electronAPI.storeSelectedModel(modelId);
      }

      setSelectedModel(modelId);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: "Failed to update model selection.",
      }));
    }
  };

  const handleEnhancePrompt = async () => {
    // Get the prompt content based on current mode
    let promptToEnhance = "";

    if (state.currentMode === "structured") {
      // Generate combined prompt from structured sections using the same logic as the component
      const allSections =
        state.customSections.length > 0 ? state.customSections : [];
      const parts: string[] = [];

      allSections.forEach((section) => {
        const content = state.structuredSections[section.key];
        if (content && content.trim()) {
          // Remove "(Optional)" text from the label for the combined output
          const cleanLabel = section.label.replace(/\s*\(Optional\)/i, "");
          parts.push(`**${cleanLabel.toUpperCase()}:**\n${content}`);
        }
      });

      promptToEnhance = parts.join("\n\n");
    } else {
      promptToEnhance = state.originalPrompt;
    }

    if (!promptToEnhance.trim()) {
      setState((prev) => ({
        ...prev,
        error: "Please enter a prompt to enhance.",
      }));
      return;
    }

    if (!geminiService.isInitialized()) {
      setShowApiKeyModal(true);
      return;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await geminiService.enhancePrompt({
        originalPrompt: promptToEnhance,
        mode: state.currentMode,
        style: state.currentStyle,
      });

      setState((prev) => ({
        ...prev,
        enhancedPrompt: response.enhancedPrompt,
        isLoading: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error:
          error instanceof Error
            ? error.message
            : "An error occurred while enhancing the prompt.",
        isLoading: false,
      }));
    }
  };

  const handleModeChange = (mode: EnhancementMode) => {
    setState((prev) => ({ ...prev, currentMode: mode }));
  };

  const handleStyleChange = (style: EnhancementStyle) => {
    setState((prev) => ({ ...prev, currentStyle: style }));
  };

  const handlePromptChange = (prompt: string) => {
    setState((prev) => ({ ...prev, originalPrompt: prompt }));
  };

  const handleClearError = () => {
    setState((prev) => ({ ...prev, error: null }));
  };

  const handleStructuredSectionsChange = (
    sections: StructuredPromptSections
  ) => {
    setState((prev) => ({ ...prev, structuredSections: sections }));
  };

  const handleTemplateSelect = (template: PromptTemplate | null) => {
    setState((prev) => ({ ...prev, selectedTemplate: template }));
  };

  const handleCustomSectionsChange = (sections: SectionConfig[]) => {
    setState((prev) => ({ ...prev, customSections: sections }));
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Prompt Enhancer</h1>
          <div className="flex items-center space-x-4">
            {/* Mode Selection */}
            <select
              value={state.currentMode}
              onChange={(e) =>
                handleModeChange(e.target.value as EnhancementMode)
              }
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="quick">Quick Enhancement</option>
              <option value="structured">Structured Prompt Builder</option>
              <option value="template">Template Generator</option>
            </select>

            {/* Style Selection */}
            <select
              value={state.currentStyle}
              onChange={(e) =>
                handleStyleChange(e.target.value as EnhancementStyle)
              }
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="detailed">Detailed</option>
              <option value="concise">Concise</option>
              <option value="creative">Creative</option>
              <option value="technical">Technical</option>
            </select>

            {/* API Key Button */}
            <button
              onClick={() => setShowApiKeyModal(true)}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              {state.apiKey ? "Update API Key" : "Set API Key"}
            </button>
          </div>
        </div>
      </header>

      {/* Error Banner */}
      {state.error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4 rounded">
          <div className="flex items-center justify-between">
            <p className="text-red-700">{state.error}</p>
            <button
              onClick={handleClearError}
              className="text-red-400 hover:text-red-600"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Model Selection */}
      {state.apiKey && (
        <div className="px-6 mt-4">
          <ModelSelector
            selectedModel={selectedModel}
            onModelSelect={handleModelSelect}
            disabled={state.isLoading}
          />
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1 p-6">
        {state.currentMode === "structured" ? (
          <div className="h-full flex space-x-6">
            <div className="flex-1">
              <StructuredPromptEditor
                sections={state.structuredSections}
                onSectionsChange={handleStructuredSectionsChange}
                customSections={state.customSections}
                onCustomSectionsChange={handleCustomSectionsChange}
                isLoading={state.isLoading}
                onEnhance={handleEnhancePrompt}
              />
            </div>
            {state.enhancedPrompt && (
              <div className="w-1/2">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">
                      Enhanced Structured Prompt
                    </h2>
                  </div>
                  <div className="h-full p-4">
                    <div className="h-full overflow-auto">
                      <pre className="whitespace-pre-wrap text-sm text-gray-800">
                        {state.enhancedPrompt}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : state.currentMode === "template" ? (
          <div className="h-full flex space-x-6">
            <div className="flex-1">
              <TemplateEditor
                originalPrompt={state.originalPrompt}
                selectedTemplate={state.selectedTemplate}
                onPromptChange={handlePromptChange}
                onTemplateSelect={handleTemplateSelect}
                isLoading={state.isLoading}
                onEnhance={handleEnhancePrompt}
              />
            </div>
            {state.enhancedPrompt && (
              <div className="w-1/2">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                  <div className="px-4 py-3 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">
                      Generated Template
                    </h2>
                  </div>
                  <div className="h-full p-4">
                    <div className="h-full overflow-auto">
                      <pre className="whitespace-pre-wrap text-sm text-gray-800">
                        {state.enhancedPrompt}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <Editor
            originalPrompt={state.originalPrompt}
            enhancedPrompt={state.enhancedPrompt}
            isLoading={state.isLoading}
            onPromptChange={handlePromptChange}
            onEnhance={handleEnhancePrompt}
          />
        )}
      </main>

      {/* API Key Modal */}
      {showApiKeyModal && (
        <ApiKeyModal
          onSubmit={handleApiKeySubmit}
          onClose={() => setShowApiKeyModal(false)}
        />
      )}
    </div>
  );
}

export default App;
