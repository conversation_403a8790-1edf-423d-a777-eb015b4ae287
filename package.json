{"name": "prompt-enhancer", "version": "1.0.0", "description": "Desktop app for enhancing prompts using Google's Gemini AI", "main": "dist/electron/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:react\" \"npm run dev:electron\"", "dev:react": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "build": "npm run build:react && npm run build:electron", "build:react": "vite build", "build:electron": "tsc -p electron/tsconfig.json", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux"}, "keywords": ["electron", "react", "typescript", "ai", "prompt-enhancement", "gemini"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "dotenv": "^16.5.0", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.8", "wait-on": "^7.2.0"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@monaco-editor/react": "^4.6.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "build": {"appId": "com.promptenhancer.app", "productName": "Prompt Enhancer", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}