import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
import { ElectronAPI } from "../src/types"; // Import the ElectronAPI interface

// Define the API that will be exposed to the renderer process
const electronAPI: ElectronAPI = {
  // API key management
  storeApiKey: (apiKey: string) => ipcRenderer.invoke("store-api-key", apiKey),
  getApiKey: () => ipc<PERSON>enderer.invoke("get-api-key"),

  // Model selection management
  storeSelectedModel: (modelId: string) =>
    ipcRenderer.invoke("store-selected-model", modelId),
  getSelectedModel: () => ipcRenderer.invoke("get-selected-model"),

  // File operations
  showSaveDialog: () => ipcRenderer.invoke("show-save-dialog"),
  showOpenDialog: () => ipcRenderer.invoke("show-open-dialog"),

  // Platform info
  platform: process.platform,
  isDev: () => ipcRenderer.invoke("is-dev"),
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld("electronAPI", electronAPI);
